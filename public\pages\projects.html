<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Projects | <PERSON></title>
  <link rel="stylesheet" href="../src/styles.css">
  <link rel="stylesheet" href="https://unpkg.com/tippy.js@6.3.1/dist/tippy.css">
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
  <script src="https://unpkg.com/@popperjs/core@2/dist/umd/popper.min.js"></script>
  <script src="https://unpkg.com/tippy.js@6.3.1/dist/tippy-bundle.umd.min.js"></script>
  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" integrity="sha384-4LISF5TTJX/fLmGSxO53rV4miRxdg84mZsxmO8Rx5jGtp/LbrixFETvWa5a6sESd" crossorigin="anonymous">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/flowbite/dist/flowbite.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #06b6d4;
    --secondary-light: #67e8f9;
    --accent-color: #8b5cf6;
    --accent-light: #a78bfa;
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --border-color: rgba(99, 102, 241, 0.2);
    --card-bg: rgba(30, 41, 59, 0.8);
    --glass-bg: rgba(15, 23, 42, 0.9);
    --shadow-primary: rgba(99, 102, 241, 0.25);
    --shadow-secondary: rgba(6, 182, 212, 0.15);
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.bg-animation::before {
    content: '';
    position: absolute;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    border-radius: 50%;
    animation: move-glow 15s infinite alternate;
    opacity: 0.3;
}

@keyframes move-glow {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(150vw, 150vh) rotate(360deg);
    }
}

.navbar-item {
    position: relative;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.navbar-item::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.navbar-item:hover {
    color: var(--primary-color);
}

.navbar-item:hover::after {
    width: 100%;
    left: 0;
}

.section-heading {
    position: relative;
    display: inline-block;
    font-weight: 700;
    color: var(--secondary-color);
}

.section-heading::after {
    content: '';
    position: absolute;
    width: 70%;
    height: 3px;
    bottom: -10px;
    left: 15%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card {
    background: linear-gradient(120deg, rgba(24,28,43,0.95) 60%, rgba(44,182,125,0.10) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid var(--primary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 1rem;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(129, 140, 248, 0.2);
}

.skill-bar {
    height: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.shadow-neumorphic {
    box-shadow: 8px 8px 16px #0c1221, -8px -8px 16px #121c33;
}

/* Premium Button Styles */
.premium-btn, .send-message-btn, .download-cv-btn, .navbar-item, .inline-block.bg-primary-color {
    position: relative;
    display: inline-block;
    font-weight: 600;
    border-radius: 9999px;
    background: linear-gradient(90deg, #3b82f6 0%, #818cf8 50%, #c7d2fe 100%);
    color: #fff;
    box-shadow: 0 4px 24px 0 rgba(129,140,248,0.25), 0 1.5px 6px 0 rgba(59,130,246,0.15);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
}
.premium-btn:hover, .send-message-btn:hover, .download-cv-btn:hover, .navbar-item:hover, .inline-block.bg-primary-color:hover {
    background: linear-gradient(90deg, #818cf8 0%, #3b82f6 50%, #c7d2fe 100%);
    box-shadow: 0 8px 32px 0 rgba(129,140,248,0.35), 0 3px 12px 0 rgba(59,130,246,0.25);
    transform: scale(1.05);
}
.premium-btn:active, .send-message-btn:active, .download-cv-btn:active, .navbar-item:active, .inline-block.bg-primary-color:active {
    transform: scale(0.98);
}
.premium-btn .btn-glow, .send-message-btn .btn-glow, .download-cv-btn .btn-glow {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    pointer-events: none;
    box-shadow: 0 0 24px 8px #818cf8, 0 0 48px 16px #3b82f6;
    opacity: 0.5;
    animation: btn-glow-anim 2s linear infinite;
}
@keyframes btn-glow-anim {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}
.animate-wave {
  animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
  transform-origin: 70% 70%;
}
@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.08); }
}
.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
}
@keyframes pulse-slow2 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.12); }
}
.animate-pulse-slow2 {
  animation: pulse-slow2 8s ease-in-out infinite;
}
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}
.animate-float {
  animation: float 7s ease-in-out infinite;
}
  .project-section {
      transition: all 0.5s ease-in-out;
}
  .card {
      transition: all 0.5s ease-in-out;
      background: rgba(255, 255, 255, 0.02);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 24px;
    }
    .card:hover {
      background: rgba(255, 255, 255, 0.03);
      border-color: rgba(255, 255, 255, 0.1);
    }
    .active-tab {
      position: relative;
    }
    .active-tab::before {
      content: '';
      position: absolute;
      inset: -1px;
      border-radius: 9999px;
      padding: 1px;
      background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
    }
    .project-thumb {
      position: relative;
      overflow: hidden;
    }
    @keyframes shine {
      0% {
        opacity: 0.5;
      }
      50% {
        opacity: 0.8;
      }
      100% {
        opacity: 0.5;
      }
    }
    .animate-text-glow {
      animation: shine 3s infinite;
    }
  </style>
</head>
<body class="bg-background text-white min-h-screen">
  <div class="bg-animation"></div>
  <header x-data="{ isOpen: false }" class="bg-transparent fixed w-full z-50 transition-all duration-300">
    <nav class="w-full px-4 py-3 bg-transparent">
      <div class="flex flex-wrap justify-between items-center mx-auto max-w-screen-xl">
        <a href="/public/index.html#home" class="flex items-center text-2xl font-bold text-white">
          <img src="../images/logo-removebg-preview.png" alt="Logo" class="h-12 w-12 mr-2">
          Abdul Basit
        </a>
        <button data-collapse-toggle="navbar-default" type="button" class="inline-flex items-center p-2 ml-3 text-sm text-gray-400 rounded-lg md:hidden hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-600" aria-controls="navbar-default" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <svg class="w-6 h-6" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
          </svg>
        </button>
        <div class="hidden w-full md:block md:w-auto" id="navbar-default">
          <ul class="flex flex-col p-4 mt-4 border border-gray-700 rounded-lg bg-gray-900 md:flex-row md:space-x-8 md:mt-0 md:text-sm md:font-medium md:border-0 md:bg-transparent">
            <li><a href="/public/index.html#home" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">Home</a></li>
            <li><a href="/public/index.html#about" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">About</a></li>
            <li><a href="/public/pages/projects.html" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">Projects</a></li>
            <li><a href="/public/index.html#services" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">Services</a></li>
            <li><a href="/public/index.html#contact" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">Contact</a></li>
            <li><a href="https://abdulbasitmemon.hashnode.dev/" class="block py-2 pl-3 pr-4 text-white rounded md:bg-transparent md:p-0">Blog</a></li>
            <li><a href="https://nas.io/prologware-solutions-3" target="_blank" class="block py-2 pl-3 pr-4 text-white bg-primary-color rounded md:bg-primary-color md:p-0 md:px-6 md:py-2 md:rounded-full md:ml-2 hover:bg-secondary-color transition duration-300 font-semibold">Join Us</a></li>
          </ul>
        </div>
      </div>
    </nav>
  </header>
  <main class="container mx-auto px-4 py-12">
    <div class="relative mb-20 mt-20">
      <h1 class="text-6xl font-black text-center bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent animate-text-glow tracking-tight drop-shadow-lg" data-aos="fade-down">
        My Projects
      </h1>
      <p class="text-center text-xl text-gray-300 mt-6 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
        Explore my diverse portfolio of projects spanning software development, hardware engineering, and creative experiments.
      </p>
    </div>
    
    <div class="flex justify-center mb-16" data-aos="zoom-in" data-aos-delay="400">
      <div id="project-tabs" class="flex flex-wrap justify-center gap-4 px-4">
        <button class="tab-btn group relative px-8 py-3 text-base font-medium text-white transition-all duration-300 ease-in-out hover:scale-105 active-tab" data-category="software">
          <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur"></span>
          <span class="relative flex items-center">
            <i class="fas fa-laptop-code mr-2 opacity-70 group-hover:opacity-100"></i>
            Software
          </span>
        </button>
        <button class="tab-btn group relative px-8 py-3 text-base font-medium text-white transition-all duration-300 ease-in-out hover:scale-105" data-category="hardware">
          <span class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur"></span>
          <span class="relative flex items-center">
            <i class="fas fa-microchip mr-2 opacity-70 group-hover:opacity-100"></i>
            Hardware
          </span>
        </button>
        <button class="tab-btn group relative px-8 py-3 text-base font-medium text-white transition-all duration-300 ease-in-out hover:scale-105" data-category="fun">
          <span class="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-yellow-500/20 rounded-full blur"></span>
          <span class="relative flex items-center">
            <i class="fas fa-gamepad mr-2 opacity-70 group-hover:opacity-100"></i>
            Fun
          </span>
        </button>
        <button class="tab-btn group relative px-8 py-3 text-base font-medium text-white transition-all duration-300 ease-in-out hover:scale-105" data-category="side">
          <span class="absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-pink-500/20 rounded-full blur"></span>
          <span class="relative flex items-center">
            <i class="fas fa-lightbulb mr-2 opacity-70 group-hover:opacity-100"></i>
            Side
          </span>
        </button>
      </div>
    </div>
    <div id="projects-container">
      <section id="software" class="project-section">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up">
            <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
            <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative">
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <i class="fas fa-car fa-3x text-white/80 transform transition-all duration-500 group-hover:scale-110 group-hover:text-white"></i>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-indigo-300/80">2024</span>
                <span class="w-1 h-1 bg-indigo-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-indigo-300/80">MOBILE</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Carpooling App</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A real-time Android platform matching drivers and riders via GPS and Mapbox's predictive routing. Achieved 90% on-time pickup rate with 50+ active campus users.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Java</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Kotlin</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Mapbox SDK</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Firebase</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="https://vimeo.com/**********" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-indigo-500/30 group-hover/btn:to-blue-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-play mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    Watch Demo
                  </span>
                </a>
                <a href="https://github.com/urooj-marvi/Carpooling_App" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
          <div class="group card relative overflow-hidden">
            <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>
            <div class="project-thumb bg-gradient-to-br from-purple-500/80 to-indigo-400/80 flex items-center justify-center h-44 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
              <i class="fas fa-graduation-cap fa-4x text-white/90 drop-shadow-xl"></i>
            </div>
            <div class="p-10 relative z-10">
              <span class="inline-block mb-4 px-5 py-2 rounded-full bg-gradient-to-r from-purple-400/80 to-indigo-400/80 text-white text-sm font-bold tracking-widest shadow animate-pulse backdrop-blur">SOFTWARE</span>
              <h3 class="font-extrabold text-3xl mb-3 text-gradient group-hover:text-indigo-400 transition-colors duration-300">TechdioApp</h3>
              <p class="mb-6 text-slate-100 text-lg leading-relaxed">A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.</p>
              <div class="flex flex-wrap gap-3 mb-6">
                <span class="px-4 py-1 text-sm font-medium text-white/70 border border-white/10 rounded-full">.NET MAUI</span>
                <span class="px-4 py-1 text-sm font-medium text-white/70 border border-white/10 rounded-full">C# & XAML</span>
                <span class="px-4 py-1 text-sm font-medium text-white/70 border border-white/10 rounded-full">Firebase</span>
                <span class="px-4 py-1 text-sm font-medium text-white/70 border border-white/10 rounded-full">LiteDB</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-500/30 group-hover/btn:to-indigo-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    View Project
                  </span>
                </a>
                <a href="https://github.com/abm1119/TechdioApp" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
          <!-- Student Management App -->
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay="200">
            <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative">
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <i class="fas fa-users fa-3x text-white/80 transform transition-all duration-500 group-hover:scale-110 group-hover:text-white"></i>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-indigo-300/80">2024</span>
                <span class="w-1 h-1 bg-indigo-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-indigo-300/80">WEB</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Student Management App</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A web-based system for academic monitoring by class advisors. Built in ASP.NET with SQL integration.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">ASP.NET Web Forms</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C#</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQL Server</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-indigo-500/30 group-hover/btn:to-blue-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    View Project
                  </span>
                </a>
                <a href="https://github.com/abm1119/student-management-app" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
          <!-- Password Manager Vault -->
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay="400">
            <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative">
              <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <i class="fas fa-key fa-3x text-white/80 transform transition-all duration-500 group-hover:scale-110 group-hover:text-white"></i>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-indigo-300/80">2024</span>
                <span class="w-1 h-1 bg-indigo-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-indigo-300/80">DESKTOP</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Password Manager Vault</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">Securely store and organize your credentials locally with encrypted storage, folder organization, and themed UI. No cloud, no tracking.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Python</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Tkinter</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQLite</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">JSON</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="https://github.com/abm1119/password-manager-vault/blob/main/build/app/app.exe" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-indigo-500/30 group-hover/btn:to-blue-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-download mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    Download App
                  </span>
                </a>
                <a href="https://github.com/abm1119/password-manager-vault" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section id="hardware" class="project-section hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up">
            <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
            <div class="relative group">
              <div class="absolute -inset-1 bg-gradient-to-r from-green-500 via-blue-500 to-teal-500 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition duration-500"></div>
              <div class="relative bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/90 rounded-2xl p-3 backdrop-blur-xl border border-white/5">
                <div class="relative group/image overflow-hidden rounded-xl aspect-video">
                  <div class="absolute inset-0 bg-grid-white/5 mask-image-gradient"></div>
                  <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-500/80 to-blue-400/80">
                    <i class="fas fa-microchip fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover/image:scale-110"></i>
                  </div>
                  <div class="absolute inset-0 bg-gradient-to-br from-green-500/60 via-blue-500/50 to-teal-500/60 
                              opacity-0 group-hover/image:opacity-90 transition-all duration-500 rounded-xl">
                    <div class="absolute inset-0 flex flex-col items-center justify-center opacity-0 group-hover/image:opacity-100 
                                transform group-hover/image:translate-y-0 translate-y-4 transition-all duration-500">
                      <span class="text-white/90 text-lg font-bold tracking-wide drop-shadow-lg">Preview</span>
                    </div>
                  </div>
                  <div class="absolute top-3 left-3 w-2 h-2 border-l-2 border-t-2 border-white/20 rounded-tl-lg"></div>
                  <div class="absolute top-3 right-3 w-2 h-2 border-r-2 border-t-2 border-white/20 rounded-tr-lg"></div>
                  <div class="absolute bottom-3 left-3 w-2 h-2 border-l-2 border-b-2 border-white/20 rounded-bl-lg"></div>
                  <div class="absolute bottom-3 right-3 w-2 h-2 border-r-2 border-b-2 border-white/20 rounded-br-lg"></div>
                </div>
              </div>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-green-300/80">2024</span>
                <span class="w-1 h-1 bg-green-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-green-300/80">HARDWARE</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Arduino Vehicle Speed Detector</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">An IR-based Arduino project that calculates and displays vehicle speed using sensor break-beams and LCD output.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Arduino Uno</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">IR Sensors</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C++</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-green-500/30 group-hover/btn:to-blue-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    View Project
                  </span>
                </a>
                <a href="https://github.com/abm1119/Arduino-Vehicle-Speed-Detector-" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section id="fun" class="project-section hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          <div class="group card relative overflow-hidden">
            <div class="project-thumb bg-gradient-to-br from-pink-500/80 to-yellow-400/80 flex items-center justify-center h-44 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
              <i class="fas fa-gamepad fa-4x text-white/90 drop-shadow-xl"></i>
            </div>
            <div class="p-10 relative z-10">
              <span class="inline-block mb-4 px-5 py-2 rounded-full bg-gradient-to-r from-pink-400/80 to-yellow-400/80 text-white text-sm font-bold tracking-widest shadow animate-pulse backdrop-blur">FUN</span>
              <h3 class="font-extrabold text-3xl mb-3 text-gradient group-hover:text-yellow-400 transition-colors duration-300">Mini JavaScript Game</h3>
              <p class="mb-6 text-slate-100 text-lg leading-relaxed">A browser-based mini game built with JavaScript for fun and learning.</p>
              <a href="#" class="inline-block mt-2 px-8 py-3 rounded-full bg-gradient-to-r from-pink-400/80 to-yellow-400/80 text-white font-semibold shadow-xl hover:scale-105 hover:shadow-2xl transition-all duration-300 border border-white/10 backdrop-blur-lg">View Project</a>
            </div>
          </div>
        </div>
      </section>
      <section id="side" class="project-section hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
          <!-- My Learning Track -->
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up">
            <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative">
              <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <i class="fas fa-graduation-cap fa-3x text-white/80 transform transition-all duration-500 group-hover:scale-110 group-hover:text-white"></i>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-yellow-300/80">2024</span>
                <span class="w-1 h-1 bg-yellow-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-yellow-300/80">LEARNING</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">My Learning Track</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A personal learning hub with a custom HTML/CSS landing page, linked to structured Notion pages published using Super.so.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">HTML</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">CSS</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Notion</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Super.so</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="https://abm1119.github.io/My-Learning-Track/" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-pink-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-yellow-500/30 group-hover/btn:to-pink-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    View Project
                  </span>
                </a>
                <a href="https://github.com/abm1119/My-Learning-Track" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
          <!-- Image Background Removal App -->
          <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay="200">
            <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative">
              <div class="absolute inset-0 bg-gradient-to-br from-pink-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <i class="fas fa-image fa-3x text-white/80 transform transition-all duration-500 group-hover:scale-110 group-hover:text-white"></i>
            </div>
            <div class="p-8 relative">
              <div class="flex items-center gap-3 mb-3">
                <span class="text-xs font-medium text-pink-300/80">2024</span>
                <span class="w-1 h-1 bg-pink-300/50 rounded-full"></span>
                <span class="text-xs font-medium text-pink-300/80">TOOL</span>
              </div>
              <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Image Background Removal</h3>
              <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A Streamlit-based tool to remove image backgrounds using Python's rembg library. Simple, fast, and effective.</p>
              <div class="flex flex-wrap gap-2 mb-6">
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Python</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Streamlit</span>
                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">rembg</span>
              </div>
              <div class="flex items-center gap-3">
                <a href="https://abm-bg-remover.streamlit.app/" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                  <span class="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-pink-500/30 group-hover/btn:to-purple-500/30"></span>
                  <span class="relative flex items-center justify-center">
                    <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                    Try It Out
                  </span>
                </a>
                <a href="https://github.com/abm1119/image_bg-Removal-app-" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                  <i class="fab fa-github fa-lg"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </main>
  <!-- Unified Premium Footer Start -->
  <footer class="footer-premium mt-16 relative">
    <div class="footer-glow"></div>
    <div class="footer-content container mx-auto px-5 py-10 grid grid-cols-1 md:grid-cols-4 gap-10">
      <div>
        <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="flex flex-col items-center mb-4">
          <img src="../images/logo-removebg-preview.png" class="h-24 transition-transform transform hover:scale-110" alt="ABM Logo" />
          <span class="mt-2 text-white font-semibold text-lg dark:text-white">Abdul Basit Memon</span>
        </a>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Visit Here</h2>
        <ul class="space-y-2">
          <li><a href="/public/index.html#services" class="footer-link">Services</a></li>
          <li><a href="/public/pages/projects.html" class="footer-link">Projects</a></li>
          <li><a href="https://abdulbasitmemon.notion.site/d9cdb927bf1f48b6ad6abc54cf5b86a1?pvs=74" target="_blank" class="footer-link">My Notion</a></li>
        </ul>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Prologware Solutions</h2>
        <ul class="space-y-2">
          <li><a href="https://resources-heaven.super.site/" target="_blank" class="footer-link">Resources Heaven</a></li>
          <li><a href="https://chat.whatsapp.com/H4mF4unGvDI3vgCFMDl3Vs" target="_blank" class="footer-link">Internships & Jobs</a></li>
          <li><a href="https://xtiles.app/65a2a3cc83f7541b54f335f8" target="_blank" class="footer-link">Community</a></li>
        </ul>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Additional Info</h2>
        <ul class="space-y-2">
          <li><a href="/public/community-guidelines.html" class="footer-link">Community Guideline</a></li>
          <li><a href="https://docs.google.com/forms/d/e/1FAIpQLSdKa4MB9goqYJYTbNhQCNE1GCjIBV43OazjQ2KU2CTXUHrNWQ/viewform" target="_blank" class="footer-link">Suggest &amp; Request</a></li>
          <li><a href="#" class="footer-link">Events</a></li>
        </ul>
      </div>
    </div>
    <hr class="footer-divider my-6 mx-auto w-11/12" />
    <div class="footer-content container mx-auto px-5 py-4 flex flex-col md:flex-row justify-between items-center">
      <span class="footer-copyright">© 2024 <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="footer-link">Abdul Basit MEMON™</a>. All Rights Reserved.</span>
      <div class="flex flex-row gap-4 mt-4 md:mt-0 items-center justify-center">
        <a href="https://www.linkedin.com/in/abdul-basit-memon-614961166/" target="_blank" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="LinkedIn"><i class="fa-brands fa-linkedin-in fa-lg"></i></a>
        <a href="https://x.com/AbdAbdulbasit1" target="_blank" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="Twitter"><i class="bi bi-twitter-x"></i></a>
        <a href="https://github.com/abm1119" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="GitHub"><i class="fa-brands fa-github fa-lg"></i></a>
        <a href="https://nas.io/prologware-solutions-3" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="WhatsApp"><i class="fa-brands fa-whatsapp fa-lg"></i></a>
        <a href="https://www.instagram.com/abmemon.memon.9/" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="Instagram"><i class="fa-brands fa-instagram fa-lg"></i></a>
      </div>
    </div>
  </footer>
  <script src="../js/projects.js"></script>
</body>
</html>
