<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON> - <PERSON>, AI/ML Engineer, and Founder of Prologware & Techdio. Specializing in web development, mobile apps, and innovative tech solutions.">
    <meta name="keywords" content="<PERSON>, Full Stack Developer, AI Engineer, Web Development, Mobile Apps, React, .NET, Portfolio">
    <meta name="author" content="<PERSON>">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="<PERSON> | Portfolio">
    <meta property="og:description" content="Full Stack Developer & AI Engineer specializing in innovative tech solutions">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://engrabm.com">
    <title><PERSON> | Portfolio</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"></noscript>
    <link rel="preload" href="./images/abm.png" as="image">

    <!-- Critical CSS -->
    <link rel="stylesheet" href="./src/styles.css">
    <link rel="stylesheet" href="./src/footer-premium.css">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">

    <!-- External CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Defer non-critical JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body>
    <!-- Simple Minimalist Navbar -->
    <nav class="navbar fixed w-full top-0 z-50 bg-[#171723]/90 backdrop-blur-sm border-b border-white/5" 
         x-data="{ isOpen: false }" 
         @keydown.escape="isOpen = false">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Brand -->
                <a href="#home" class="flex items-center">
                    <span class="text-white font-medium text-lg tracking-wide">ABM</span>
                </a>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="#home" class="nav-link">Home</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#projects" class="nav-link">Projects</a>
                <a href="#services" class="nav-link">Services</a>
                <a href="#contact" class="nav-link">Contact</a>
                <a href="https://abdulbasitmemon.hashnode.dev/" 
                   class="nav-link" 
                   target="_blank" 
                   rel="noopener">Blog</a>
                <a href="https://nas.io/prologware-solutions-3"
                   class="inline-flex items-center px-4 py-2 rounded-lg bg-indigo-600 text-white text-sm font-medium hover:bg-indigo-700 transition-colors"
                   target="_blank"
                   rel="noopener">
                    <i class="fas fa-users mr-2"></i>
                    Join Community
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <button class="md:hidden text-gray-300 hover:text-white"
                    @click="isOpen = !isOpen"
                    :aria-expanded="isOpen"
                    aria-label="Toggle navigation menu">
                <svg class="h-6 w-6" 
                     :class="{ 'hidden': isOpen }" 
                     stroke="currentColor" 
                     fill="none" 
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" 
                          stroke-linejoin="round" 
                          stroke-width="2" 
                          d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
                <svg class="h-6 w-6" 
                     :class="{ 'hidden': !isOpen }" 
                     stroke="currentColor" 
                     fill="none" 
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round" 
                          stroke-linejoin="round" 
                          stroke-width="2" 
                          d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
            </div>
        </div>

        <!-- Simple Mobile Menu -->
        <div class="md:hidden"
             x-show="isOpen"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 -translate-y-1"
             x-transition:enter-end="opacity-100 translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 translate-y-0"
             x-transition:leave-end="opacity-0 -translate-y-1"
             @click.away="isOpen = false"
             style="display: none;">

            <div class="bg-[#171723] border-t border-white/5">
                <div class="px-4 py-2 space-y-1">
                    <a href="#home" @click="isOpen = false" 
                       class="mobile-link">
                        <i class="fas fa-home w-5"></i>
                        <span>Home</span>
                    </a>
                    <a href="#about" @click="isOpen = false" 
                       class="mobile-link">
                        <i class="fas fa-user w-5"></i>
                        <span>About</span>
                    </a>
                    <a href="#projects" @click="isOpen = false" 
                       class="mobile-link">
                        <i class="fas fa-code w-5"></i>
                        <span>Projects</span>
                    </a>
                    <a href="#services" @click="isOpen = false" 
                       class="mobile-link">
                        <i class="fas fa-cogs w-5"></i>
                        <span>Services</span>
                    </a>
                    <a href="#contact" @click="isOpen = false" 
                       class="mobile-link">
                        <i class="fas fa-envelope w-5"></i>
                        <span>Contact</span>
                    </a>
                    <a href="https://abdulbasitmemon.hashnode.dev/"
                       target="_blank"
                       rel="noopener"
                       class="mobile-link">
                        <i class="fas fa-blog w-5"></i>
                        <span>Blog</span>
                    </a>
                    <a href="https://nas.io/prologware-solutions-3"
                       target="_blank"
                       rel="noopener"
                       class="block px-4 py-2 mt-4 text-center text-white bg-indigo-600 rounded-lg hover:bg-indigo-700"
                       @click="isOpen = false">
                        <i class="fas fa-users mr-2"></i>
                        Join Community
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <style>
    .nav-link {
        color: rgb(209 213 219);
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.2s;
        position: relative;
        padding: 0.5rem 0;
    }
    
    .nav-link:hover {
        color: white;
    }
    
    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: rgb(99 102 241);
        transition: width 0.2s;
    }
    
    .nav-link:hover::after {
        width: 100%;
    }
    
    .mobile-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        color: rgb(209 213 219);
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border-radius: 0.5rem;
    }
    
    .mobile-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.05);
    }
    </style>

        <main style="padding-top: 80px;">

<!-- Home Section -->
<section id="home" class="relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Optimized background gradients -->
    <div class="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary"></div>

    <!-- Animated background elements -->
    <div class="absolute inset-0 pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-primary-color/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div class="absolute bottom-1/4 right-1/4 w-48 h-48 md:w-80 md:h-80 bg-gradient-to-tr from-secondary-color/15 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-accent-color/10 to-transparent rounded-full blur-2xl animate-float"></div>
    </div>
    <div class="container relative z-10 text-center py-20">
        <div class="max-w-4xl mx-auto space-y-8">
            <!-- Premium Hero Typography -->
            <div class="space-y-6" data-animate>
                <h1 class="title-responsive font-bold tracking-tight leading-tight">
                    <span id="waveHand" class="inline-block text-4xl md:text-6xl cursor-pointer transition-transform duration-300 hover:scale-110 touch-target">👋</span>
                    <span class="block mt-3 bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent font-extrabold drop-shadow-lg">
                        Abdul Basit Memon
                    </span>
                </h1>

                <div class="space-y-3">
                    <h2 class="heading-responsive font-semibold bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                        Design. Code. Inspire.
                    </h2>
                    <p class="text-responsive text-slate-400 font-light tracking-wide">
                        Minimalist. Maker. Dreamer.
                    </p>
                </div>
            </div>

            <!-- Premium Role Badge -->
            <div class="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white font-semibold shadow-lg text-sm md:text-base tracking-widest uppercase touch-target" data-animate>
                <i class="fas fa-rocket"></i>
                Founder, Prologware & Techdio
            </div>

            <!-- Premium Description -->
            <p class="text-responsive text-slate-300 max-w-2xl mx-auto leading-relaxed font-light" data-animate>
                I craft digital experiences that blend
                <span class="font-semibold text-indigo-400">clarity</span> and
                <span class="font-semibold text-blue-400">creativity</span>.
                <br class="hidden sm:block">
                From code to concept, I believe in less, but better.
            </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4" data-animate>
            <a href="./images/Resume.pdf" target="_blank" class="btn btn-primary btn-lg group touch-target" aria-label="Download Abdul Basit Memon's Resume">
                <i class="fas fa-download"></i>
                <span>Download Resume</span>
                <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
            </a>
            <a href="#contact" class="btn btn-secondary btn-lg group touch-target" aria-label="Contact Abdul Basit Memon">
                <span class="text-2xl">💬</span> Let’s Connect
                <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
            </a>
        </div>
            <!-- Premium Skills Badges -->
            <div class="flex flex-wrap gap-3 justify-center pt-8" data-animate>
                <span class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#232b47] text-indigo-400 font-medium shadow-md border border-indigo-500/30 text-sm md:text-base">
                    <i class="fas fa-code"></i>Full Stack
                </span>
                <span class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#232b47] text-purple-400 font-medium shadow-md border border-purple-500/30 text-sm md:text-base">
                    <i class="fas fa-lightbulb"></i>Creative
                </span>
                <span class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#232b47] text-blue-400 font-medium shadow-md border border-blue-500/30 text-sm md:text-base">
                    <i class="fas fa-rocket"></i>Startup
                </span>
                <span class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#232b47] text-indigo-300 font-medium shadow-md border border-indigo-400/30 text-sm md:text-base">
                    <i class="fas fa-graduation-cap"></i>Educator
                </span>
            </div>
        </div>
    </div>
</section>
<script>
// Enhanced JavaScript for better functionality
document.addEventListener('DOMContentLoaded', function() {
    // Waving hand animation on click
    const waveHand = document.getElementById('waveHand');
    if (waveHand) {
        waveHand.addEventListener('click', function () {
            waveHand.classList.add('animate-wave');
            setTimeout(() => waveHand.classList.remove('animate-wave'), 1200);
        });
    }

    // Enhanced smooth scrolling and active navigation
    const navLinks = document.querySelectorAll('.navbar-item, .mobile-menu-item');
    const sections = document.querySelectorAll('section[id]');

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const navbarHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = target.offsetTop - navbarHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Active navigation highlighting
    function updateActiveNavigation() {
        const scrollPosition = window.scrollY + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                // Remove active class from all nav items
                navLinks.forEach(link => link.classList.remove('active'));

                // Add active class to current section's nav items
                const activeLinks = document.querySelectorAll(`a[href="#${sectionId}"]`);
                activeLinks.forEach(link => {
                    if (link.classList.contains('navbar-item') || link.classList.contains('mobile-menu-item')) {
                        link.classList.add('active');
                    }
                });
            }
        });
    }

    // Throttled scroll listener for active navigation
    let navigationTicking = false;
    window.addEventListener('scroll', () => {
        if (!navigationTicking) {
            requestAnimationFrame(() => {
                updateActiveNavigation();
                navigationTicking = false;
            });
            navigationTicking = true;
        }
    }, { passive: true });

    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        lastScrollY = currentScrollY;
    });

    // Enhanced mobile menu functionality
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const hamburger = document.querySelector('.hamburger');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');

    function closeMobileMenu() {
        if (hamburger) hamburger.classList.remove('active');
        if (mobileMenu) mobileMenu.classList.remove('open');
        if (mobileMenuOverlay) mobileMenuOverlay.classList.remove('open');
        document.body.style.overflow = '';

        // Update ARIA attributes
        if (mobileMenuToggle) {
            mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }
    }

    function openMobileMenu() {
        if (hamburger) hamburger.classList.add('active');
        if (mobileMenu) mobileMenu.classList.add('open');
        if (mobileMenuOverlay) mobileMenuOverlay.classList.add('open');
        document.body.style.overflow = 'hidden';

        // Update ARIA attributes
        if (mobileMenuToggle) {
            mobileMenuToggle.setAttribute('aria-expanded', 'true');
        }
    }

    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', () => {
            const isOpen = mobileMenu.classList.contains('open');
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        });

        // Close menu when clicking overlay
        if (mobileMenuOverlay) {
            mobileMenuOverlay.addEventListener('click', closeMobileMenu);
        }

        // Close menu when clicking close button
        if (mobileMenuClose) {
            mobileMenuClose.addEventListener('click', closeMobileMenu);
        }

        // Close mobile menu when clicking on links
        document.querySelectorAll('.mobile-menu-item, .mobile-menu-cta').forEach(link => {
            link.addEventListener('click', () => {
                // Add a small delay for better UX
                setTimeout(closeMobileMenu, 150);
            });
        });
    }

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.card, .glass-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Enhanced touch interactions for mobile
    let touchStartY = 0;
    let touchEndY = 0;

    document.addEventListener('touchstart', e => {
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    document.addEventListener('touchend', e => {
        touchEndY = e.changedTouches[0].screenY;
        handleSwipe();
    }, { passive: true });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartY - touchEndY;

        if (Math.abs(diff) > swipeThreshold) {
            // Add subtle feedback for swipe gestures
            document.body.style.transform = `translateY(${diff > 0 ? -2 : 2}px)`;
            setTimeout(() => {
                document.body.style.transform = '';
            }, 150);
        }
    }

    // Performance optimization: Throttle scroll events
    let ticking = false;

    function updateOnScroll() {
        const currentScrollY = window.scrollY;

        if (currentScrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    }, { passive: true });

    // Add loading states for better UX
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');

        // Fade in elements after load
        setTimeout(() => {
            document.querySelectorAll('[data-animate]').forEach(el => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            });
        }, 100);
    });

    // Preload critical images
    const criticalImages = [
        './images/abm.png',
        './images/logo-removebg-preview.png'
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });

    // Add focus management for accessibility
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            // Close mobile menu on escape
            if (mobileMenu && mobileMenu.classList.contains('open')) {
                hamburger.classList.remove('active');
                mobileMenu.classList.remove('open');
                mobileMenuOverlay.classList.remove('open');
                document.body.style.overflow = '';
            }
        }
    });

    // Add visual feedback for button interactions
    document.querySelectorAll('.btn, .navbar-item').forEach(btn => {
        btn.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        }, { passive: true });

        btn.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        }, { passive: true });
    });

    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log(`Page load time: ${perfData.loadEventEnd - perfData.loadEventStart}ms`);
                }
            }, 0);
        });
    }

    // Register service worker for better caching (optional)
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
            // Uncomment the following lines if you want to add a service worker
            // navigator.serviceWorker.register('/sw.js')
            //     .then(registration => console.log('SW registered'))
            //     .catch(error => console.log('SW registration failed'));
        });
    }
});
</script>
<style>
@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}
.animate-wave {
  animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
  transform-origin: 70% 70%;
}
@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.08); }
}
.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
}
@keyframes pulse-slow2 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.12); }
}
.animate-pulse-slow2 {
  animation: pulse-slow2 8s ease-in-out infinite;
}
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}
.animate-float {
  animation: float 7s ease-in-out infinite;
}
</style>

<!-- About Me -->
<section id="about" class="py-16 md:py-24">
    <div class="container mx-auto">
        <h2 class="section-heading text-center mb-12 md:mb-16" data-animate>About Me</h2>
        <div class="flex flex-col md:flex-row items-center justify-center gap-16">
            <div class="md:w-1/3 flex justify-center">
                <div class="relative group flex items-center justify-center">
                    <div class="absolute w-80 h-80 bg-gradient-to-tr from-indigo-500 via-blue-400 to-purple-500 rounded-full blur-2xl opacity-40 animate-pulse-slow z-0"></div>
                    <img src="./images/abm.png" alt="Abdul Basit Memon - Full Stack Developer and AI Engineer"
                         class="rounded-full w-72 h-72 object-cover object-center shadow-2xl border-8 border-white group-hover:scale-105 transition-transform duration-500 z-10"
                         loading="lazy"
                         width="288"
                         height="288">
                    <div class="absolute inset-0 rounded-full border-4 border-primary-color/40 pointer-events-none animate-pulse"></div>
                </div>
            </div>
            <div class="md:w-2/3 flex flex-col gap-6">
                <div class="relative bg-gradient-to-br from-[#232b47] via-[#181c2a] to-[#232b47] rounded-3xl shadow-2xl p-10 border-0 overflow-hidden group transition-all duration-500 hover:scale-[1.025]">
                    <div class="absolute -top-10 -left-10 w-40 h-40 bg-gradient-to-tr from-indigo-500 via-blue-400 to-purple-500 rounded-full blur-2xl opacity-30 animate-pulse-slow"></div>
                    <div class="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-tr from-purple-500 via-indigo-400 to-blue-500 rounded-full blur-2xl opacity-20 animate-pulse-slow2"></div>
                    <h3 class="font-poppins text-3xl md:text-4xl font-extrabold text-white mb-4 flex items-center gap-3 drop-shadow-lg">
                        <svg class="w-9 h-9 text-indigo-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>
                        Who I Am
                    </h3>
                    <p class="text-xl md:text-2xl text-slate-200 font-light leading-relaxed mb-2 tracking-wide">
                        <span class="font-bold text-primary-color">Abdul Basit Memon</span> — tech enthusiast, indie hacker, and founder.<br>
                        <span class="text-slate-300">I build software that solves real-world problems and helps people grow. With a solid background in engineering, leadership, and business, I work at the intersection of <span class="font-semibold text-primary-color">code</span> and <span class="font-semibold text-primary-color">impact</span>.</span>
                    </p>
                    <div class="flex flex-wrap gap-3 mt-4">
                        <span class="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white font-semibold shadow-lg text-xs md:text-sm tracking-widest uppercase">Founder</span>
                        <span class="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-[#232b47] text-primary-color font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-primary-color/30">Engineer</span>
                        <span class="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-[#232b47] text-purple-400 font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-purple-400/30">Leader</span>
                        <span class="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-[#232b47] text-blue-400 font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-blue-400/30">Indie Hacker</span>
                    </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-2">
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-indigo-500/20">
                        <svg class="w-7 h-7 text-blue-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"/></svg>
                        <div>
                            <span class="font-semibold text-white">Always learning</span>
                            <p class="text-slate-400 text-sm mt-1"> Exploring new tech, ideas, and skills every day.</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-blue-500/20">
                        <svg class="w-7 h-7 text-green-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 17v-2a4 4 0 018 0v2M9 17a4 4 0 01-8 0v-2a4 4 0 018 0v2zm0 0h6"/></svg>
                        <div>
                            <span class="font-semibold text-white">Skilled in</span>
                            <p class="text-slate-400 text-sm mt-1"> C++, Python, Java, C#, .NET, React, and more</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-purple-500/20">
                        <svg class="w-7 h-7 text-purple-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m4 0h-1V8h-1m-4 0h1v4h1m-4 0h1v4h1"/></svg>
                        <div>
                            <span class="font-semibold text-white">Tech Interests</span>
                            <p class="text-slate-400 text-sm mt-1"> AI/ML, embedded systems, and clean UX</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-yellow-500/20">
                        <svg class="w-7 h-7 text-yellow-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 2a10 10 0 100 20 10 10 0 000-20zm0 4a6 6 0 110 12A6 6 0 0112 6z"/></svg>
                        <div>
                            <span class="font-semibold text-white">Rooted in Faith</span>
                            <p class="text-slate-400 text-sm mt-1"> Guided by my Muslim values and principles</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-pink-500/20">
                        <svg class="w-7 h-7 text-pink-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 21v-2a4 4 0 014-4h10a4 4 0 014 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                        <div>
                            <span class="font-semibold text-white">Family Business</span>
                            <p class="text-slate-400 text-sm mt-1"> Entrepreneurial roots and teamwork</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-4 bg-[#232b47]/80 rounded-xl p-5 shadow border border-green-400/20">
                        <svg class="w-7 h-7 text-green-400 mt-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 14l9-5-9-5-9 5 9 5zm0 7v-7"/></svg>
                        <div>
                            <span class="font-semibold text-white">Full-time Learner & Teacher</span>
                            <p class="text-slate-400 text-sm mt-1"> Sharing knowledge, growing every day</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<!-- Skills Section -->
<section id="skills" class="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
  <div class="container mx-auto">
    <h2 class="section-heading text-center mb-12 md:mb-16" data-animate>Core Skillset</h2>
    <div class="w-full max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-16">
      <div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-indigo-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M16 18v-1a4 4 0 00-8 0v1"/><circle cx="12" cy="7" r="4"/></svg>
            <span class="text-2xl font-bold text-white">Programming Languages</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-indigo-400/60">
            <span class="font-semibold text-indigo-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-cuttlefish"></i> C++</span>
            <span class="font-semibold text-blue-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-java"></i> Java</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-python"></i> Python</span>
            <span class="font-semibold text-green-300 flex items-center gap-2 text-lg"><i class="fa-solid fa-hashtag"></i> C#</span>
          </div>
        </div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-blue-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm4 4h10"/></svg>
            <span class="text-2xl font-bold text-white">Web Development</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-blue-400/60">
            <span class="font-semibold text-pink-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-html5"></i> HTML/CSS</span>
            <span class="font-semibold text-yellow-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-js"></i> JavaScript</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-microsoft"></i> ASP.NET</span>
            <span class="font-semibold text-indigo-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-css3-alt"></i> Tailwind CSS</span>
            <span class="font-semibold text-purple-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-react"></i> React (Learning)</span>
          </div>
        </div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-purple-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="16" height="20" x="4" y="2" rx="2"/><circle cx="12" cy="18" r="1"/></svg>
            <span class="text-2xl font-bold text-white">Mobile Development</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-purple-400/60">
            <span class="font-semibold text-purple-300 flex items-center gap-2 text-lg"><i class="fa-brands fa-microsoft"></i> .NET MAUI</span>
            <span class="font-semibold text-green-300 flex items-center gap-2 text-lg"><i class="fa-solid fa-hashtag"></i> C#</span>
            <span class="font-semibold text-pink-300 flex items-center gap-2 text-lg"><i class="fa-solid fa-code"></i> XML</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-windows"></i> Visual Studio</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-java"></i> Java</span>
            <span class="font-semibold text-green-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-android"></i> Android Studio</span>
          </div>
        </div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-green-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M3 5v14c0 1.657 4.03 3 9 3s9-1.343 9-3V5"/></svg>
            <span class="text-2xl font-bold text-white">Databases</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-green-400/60">
            <span class="font-semibold text-green-400 flex items-center gap-2 text-lg"><i class="fa-brands fa-google"></i> Firebase</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-database"></i> MySQL</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-database"></i> Supabase</span>
            <span class="font-semibold text-purple-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-database"></i> PostgreSQL</span>
            <span class="font-semibold text-green-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-database"></i> SSMS</span>
          </div>
        </div>
      </div>
      <div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-pink-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="14" x="2" y="5" rx="2"/><path d="M8 21h8"/></svg>
            <span class="text-2xl font-bold text-white">Tools, Software & Platforms</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-pink-400/60">
            <span class="font-semibold text-pink-400 flex items-center gap-2 text-lg"><i class="fa-solid fa-calculator"></i> MATLAB</span>
            <span class="font-semibold text-gray-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-git-alt"></i> Git/GitHub</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-note-sticky"></i> Notion</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-adobe"></i> Photoshop</span>
            <span class="font-semibold text-green-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-java"></i> NetBeans</span>
            <span class="font-semibold text-purple-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-microchip"></i> Xilinx Vivado</span>
            <span class="font-semibold text-pink-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-project-diagram"></i> LabVIEW</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-book"></i> Jupyter Notebooks</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-file-word"></i> Office Suite</span>
            <span class="font-semibold text-pink-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-brain"></i> MIT Scratch</span>
          </div>
        </div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-yellow-400" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><rect width="20" height="12" x="2" y="6" rx="2"/><path d="M6 18v2a2 2 0 002 2h8a2 2 0 002-2v-2"/></svg>
            <span class="text-2xl font-bold text-white">Hardware & IoT</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-yellow-400/60">
            <span class="font-semibold text-yellow-400 flex items-center gap-2 text-lg"><i class="fa-brands fa-usb"></i> Arduino UNO</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-code"></i> Arduino IDE</span>
            <span class="font-semibold text-green-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-wifi"></i> ESP32/ESP8266</span>
            <span class="font-semibold text-pink-200 flex items-center gap-2 text-lg"><i class="fa-brands fa-raspberry-pi"></i> Raspberry Pi</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-cube"></i> PictoBlox</span>
          </div>
        </div>
        <div class="mb-10">
          <div class="flex items-center gap-3 mb-2">
            <svg class="w-7 h-7 text-green-300" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 8c-1.657 0-3 1.343-3 3s1.343 3 3 3 3-1.343 3-3-1.343-3-3-3zm0 10c-4.418 0-8-1.79-8-4V6c0-2.21 3.582-4 8-4s8 1.79 8 4v8c0 2.21-3.582 4-8 4z"/></svg>
            <span class="text-2xl font-bold text-white">Soft Skills</span>
          </div>
          <div class="flex flex-wrap gap-4 pl-10 border-l-4 border-green-300/60">
            <span class="font-semibold text-green-300 flex items-center gap-2 text-lg"><i class="fa-solid fa-lightbulb"></i> Problem-solving</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-comments"></i> Clear communication</span>
            <span class="font-semibold text-purple-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-users"></i> Team collaboration</span>
            <span class="font-semibold text-yellow-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-chess-king"></i> Leadership mindset</span>
            <span class="font-semibold text-pink-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-clock"></i> Time management</span>
            <span class="font-semibold text-green-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-infinity"></i> Continuous learning</span>
            <span class="font-semibold text-blue-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-shuffle"></i> Adaptability</span>
            <span class="font-semibold text-purple-200 flex items-center gap-2 text-lg"><i class="fa-solid fa-paint-brush"></i> Creativity</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!---Projects Section-->
<section id="projects" class="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
  <div class="container mx-auto">
    <div class="max-w-3xl mx-auto text-center mb-16 md:mb-20">
      <h2 class="section-heading mb-6" data-animate>My Projects</h2>
      <div class="mx-auto w-24 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 rounded-full mb-6"></div>
      <p class="text-lg md:text-xl text-slate-300 font-light leading-relaxed mb-2">A curated showcase of my most impactful and creative work. Each project blends thoughtful engineering, design, and real-world value.</p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-7xl mx-auto">
      <!-- TechdioApp Project -->
      <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up">
        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>
        
        <div class="project-thumb bg-gradient-to-br from-purple-500/80 to-indigo-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
          <i class="fas fa-graduation-cap fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
        </div>
        
        <div class="p-8 relative z-10">
          <div class="flex items-center gap-3 mb-3">
            <span class="text-xs font-medium text-purple-300/80">2024</span>
            <span class="w-1 h-1 bg-purple-300/50 rounded-full"></span>
            <span class="text-xs font-medium text-purple-300/80">SOFTWARE</span>
          </div>
          <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">TechdioApp</h3>
          <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.</p>
          
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">.NET MAUI</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C#</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQLite</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">XAML</span>
          </div>
          
          <div class="flex items-center gap-3">
            <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
              <span class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-500/30 group-hover/btn:to-indigo-500/30"></span>
              <span class="relative flex items-center justify-center">
                <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                View Project
              </span>
            </a>
            <a href="https://github.com/abm1119/TechdioApp" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
              <i class="fab fa-github fa-lg"></i>
            </a>
          </div>
        </div>
      </div>

      <!-- Carpooling App Project -->
      <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay="200">
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        
        <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative bg-gradient-to-br from-indigo-500/80 to-blue-400/80 shadow-2xl backdrop-blur-lg">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <i class="fas fa-car fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
        </div>
        
        <div class="p-8 relative">
          <div class="flex items-center gap-3 mb-3">
            <span class="text-xs font-medium text-indigo-300/80">2024</span>
            <span class="w-1 h-1 bg-indigo-300/50 rounded-full"></span>
            <span class="text-xs font-medium text-indigo-300/80">MOBILE</span>
          </div>
          <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Carpooling App</h3>
          <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A comprehensive carpooling solution built with React Native, featuring real-time ride matching, secure payments, and GPS tracking for seamless shared transportation.</p>
          
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">React Native</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Firebase</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Maps API</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Payment Gateway</span>
          </div>
          
          <div class="flex items-center gap-3">
            <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
              <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-indigo-500/30 group-hover/btn:to-blue-500/30"></span>
              <span class="relative flex items-center justify-center">
                <i class="fas fa-play mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                Watch Demo
              </span>
            </a>
            <a href="https://github.com/urooj-marvi/Carpooling_App" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
              <i class="fab fa-github fa-lg"></i>
            </a>
          </div>
        </div>
      </div>

      <!-- Arduino Speed Detector Project -->
      <div class="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay="400">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-green-400/20 via-blue-400/10 to-teal-400/0 rounded-full blur-2xl opacity-30 z-0"></div>
        
        <div class="project-thumb bg-gradient-to-br from-green-500/80 to-blue-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
          <i class="fas fa-microchip fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
        </div>
        
        <div class="p-8 relative z-10">
          <div class="flex items-center gap-3 mb-3">
            <span class="text-xs font-medium text-green-300/80">2024</span>
            <span class="w-1 h-1 bg-green-300/50 rounded-full"></span>
            <span class="text-xs font-medium text-green-300/80">HARDWARE</span>
          </div>
          <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Arduino Speed Detector</h3>
          <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">An intelligent speed detection system using Arduino and ultrasonic sensors. Features real-time monitoring, data logging, and alert mechanisms for traffic management.</p>
          
          <div class="flex flex-wrap gap-2 mb-6">
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Arduino</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C++</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Sensors</span>
            <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">IoT</span>
          </div>
          
          <div class="flex items-center gap-3">
            <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
              <span class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-green-500/30 group-hover/btn:to-blue-500/30"></span>
              <span class="relative flex items-center justify-center">
                <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                View Project
              </span>
            </a>
            <a href="#" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
              <i class="fab fa-github fa-lg"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <div class="flex justify-center mt-20">
      <a href="./pages/projects.html" class="relative premium-btn py-5 px-12 text-lg font-semibold flex items-center gap-3 shadow-xl border border-primary-color/40 rounded-full bg-gradient-to-r from-[#232b47] via-[#181c2a] to-[#232b47] hover:bg-gradient-to-r hover:from-indigo-500 hover:via-blue-500 hover:to-purple-500 transition-all duration-300 group">
        <span class="text-2xl">🚀</span>
        <span class="tracking-wide font-semibold">View More Projects</span>
        <span class="btn-glow"></span>
      </a>
    </div>
  </div>
</section>

 <!-- Services Section -->
<section id="services" class="py-16 md:py-24">
    <div class="container mx-auto">
        <div class="text-center mb-12 md:mb-16">
            <h2 class="section-heading mb-4" data-animate>Professional Services</h2>
            <div class="w-24 h-1 bg-primary-color mx-auto rounded-full mb-6"></div>
            <p class="text-text-color text-lg max-w-2xl mx-auto">Transforming ideas into digital excellence with cutting-edge solutions and innovative approaches</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            <!-- Web & Mobile Development -->
            <div class="card relative p-8 group transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/80 rounded-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
                <div class="absolute top-0 right-0 mt-6 mr-6">
                    <span class="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-primary-color/10 to-secondary-color/10 border border-primary-color/20 rounded-full text-primary-color">Enterprise Solutions</span>
                </div>
                <div class="relative flex items-center gap-6 mb-6">
                    <div class="relative">
                        <div class="absolute inset-0 bg-primary-color/20 rounded-full blur-xl group-hover:bg-primary-color/30 transition-all duration-300"></div>
                        <div class="relative bg-[#232b47] p-6 rounded-full border border-primary-color/30 group-hover:border-primary-color/60">
                            <i class="fas fa-laptop-code text-4xl text-primary-color group-hover:scale-110 transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-bold mb-2 text-secondary-color">Web & Mobile Apps</h3>
                        <p class="text-sm font-medium text-primary-color/80">Full-Stack Development</p>
                    </div>
                </div>
                <div class="space-y-6 text-text-color mb-8">
                    <div class="flex flex-col gap-3">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">React, Next.js & .NET Core Solutions</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">MAUI & React Native Mobile Apps</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Native Android with Jetpack</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <a href="./pages/web-development.html" class="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-[#232b47] text-primary-color hover:bg-primary-color hover:text-white transition-all duration-300 group border border-primary-color/20">
                        <span class="font-medium">View Projects</span>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
                    </a>
                    <span class="text-xs text-primary-color/60">Enterprise Grade</span>
                </div>
            </div>

            <!-- AI/ML Solutions -->
            <div class="card relative p-8 group transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/80 rounded-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
                <div class="absolute top-0 right-0 mt-6 mr-6">
                    <span class="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-primary-color/10 to-secondary-color/10 border border-primary-color/20 rounded-full text-primary-color">AI Innovation</span>
                </div>
                <div class="relative flex items-center gap-6 mb-6">
                    <div class="relative">
                        <div class="absolute inset-0 bg-primary-color/20 rounded-full blur-xl group-hover:bg-primary-color/30 transition-all duration-300"></div>
                        <div class="relative bg-[#232b47] p-6 rounded-full border border-primary-color/30 group-hover:border-primary-color/60">
                            <i class="fas fa-brain text-4xl text-primary-color group-hover:scale-110 transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-bold mb-2 text-secondary-color">AI/ML Solutions</h3>
                        <p class="text-sm font-medium text-primary-color/80">Intelligent Systems</p>
                    </div>
                </div>
                <div class="space-y-6 text-text-color mb-8">
                    <div class="flex flex-col gap-3">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Custom ML Model Development</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Data Analytics & Visualization</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">AI-Powered Process Automation</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <a href="#" class="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-[#232b47] text-primary-color hover:bg-primary-color hover:text-white transition-all duration-300 group border border-primary-color/20">
                        <span class="font-medium">Explore AI</span>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
                    </a>
                    <span class="text-xs text-primary-color/60">Smart Solutions</span>
                </div>
            </div>

            <!-- UI/UX Design -->
            <div class="card relative p-8 group transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/80 rounded-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
                <div class="absolute top-0 right-0 mt-6 mr-6">
                    <span class="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-primary-color/10 to-secondary-color/10 border border-primary-color/20 rounded-full text-primary-color">Creative Design</span>
                </div>
                <div class="relative flex items-center gap-6 mb-6">
                    <div class="relative">
                        <div class="absolute inset-0 bg-primary-color/20 rounded-full blur-xl group-hover:bg-primary-color/30 transition-all duration-300"></div>
                        <div class="relative bg-[#232b47] p-6 rounded-full border border-primary-color/30 group-hover:border-primary-color/60">
                            <i class="fas fa-paint-brush text-4xl text-primary-color group-hover:scale-110 transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-bold mb-2 text-secondary-color">UI/UX Design</h3>
                        <p class="text-sm font-medium text-primary-color/80">Creative Excellence</p>
                    </div>
                </div>
                <div class="space-y-6 text-text-color mb-8">
                    <div class="flex flex-col gap-3">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">User-Centered Design Process</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Interactive Prototyping</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Brand Identity Systems</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <a href="./pages/graphic-design.html" class="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-[#232b47] text-primary-color hover:bg-primary-color hover:text-white transition-all duration-300 group border border-primary-color/20">
                        <span class="font-medium">View Designs</span>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
                    </a>
                    <span class="text-xs text-primary-color/60">User-Focused</span>
                </div>
            </div>

            <!-- Technical Training -->
            <div class="card relative p-8 group transition-all duration-300 hover:-translate-y-2 bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/80 rounded-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
                <div class="absolute top-0 right-0 mt-6 mr-6">
                    <span class="px-3 py-1 text-xs font-semibold bg-gradient-to-r from-primary-color/10 to-secondary-color/10 border border-primary-color/20 rounded-full text-primary-color">Expert Training</span>
                </div>
                <div class="relative flex items-center gap-6 mb-6">
                    <div class="relative">
                        <div class="absolute inset-0 bg-primary-color/20 rounded-full blur-xl group-hover:bg-primary-color/30 transition-all duration-300"></div>
                        <div class="relative bg-[#232b47] p-6 rounded-full border border-primary-color/30 group-hover:border-primary-color/60">
                            <i class="fas fa-graduation-cap text-4xl text-primary-color group-hover:scale-110 transition-transform duration-300"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-bold mb-2 text-secondary-color">Technical Training</h3>
                        <p class="text-sm font-medium text-primary-color/80">Knowledge Transfer</p>
                    </div>
                </div>
                <div class="space-y-6 text-text-color mb-8">
                    <div class="flex flex-col gap-3">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Customized Learning Paths</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Hands-on Project Training</p>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="fas fa-check-circle text-primary-color"></i>
                            <p class="text-sm">Industry-Ready Skills</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <a href="./pages/tutoring.html" class="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-[#232b47] text-primary-color hover:bg-primary-color hover:text-white transition-all duration-300 group border border-primary-color/20">
                        <span class="font-medium">Start Learning</span>
                        <i class="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
                    </a>
                    <span class="text-xs text-primary-color/60">Professional Growth</span>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Prologware Solutions Section -->
<section id="prologware" class="py-24">
    <div class="container mx-auto px-6">
        <h2 class="section-heading text-4xl text-center mb-16">Prologware Solutions</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
            <div class="relative rounded-2xl overflow-hidden shadow-lg">
                <iframe class="w-full h-96" src="https://www.youtube.com/embed/videoID" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
            <div class="card p-10">
                <p class="text-2xl text-secondary-color mb-8 leading-relaxed">Prologware Solutions focuses on community building with the motto of <span class="font-bold text-primary-color">To Learn, Grow, Earn & Collab.</span></p>
                <ul class="space-y-6">
                    <li class="flex items-center space-x-4">
                        <i class="fas fa-star text-2xl text-primary-color"></i>
                        <span class="text-lg">Premium Courses</span>
                    </li>
                    <li class="flex items-center space-x-4">
                        <i class="fas fa-tools text-2xl text-primary-color"></i>
                        <span class="text-lg">Tools and Resources</span>
                    </li>
                    <li class="flex items-center space-x-4">
                        <i class="fas fa-graduation-cap text-2xl text-primary-color"></i>
                        <span class="text-lg">Scholarship Updates</span>
                    </li>
                    <li class="flex items-center space-x-4">
                        <i class="fas fa-briefcase text-2xl text-primary-color"></i>
                        <span class="text-lg">Internships and Jobs</span>
                    </li>
                    <li class="flex items-center space-x-4">
                        <i class="fas fa-users text-2xl text-primary-color"></i>
                        <span class="text-lg">Community Collaboration</span>
                    </li>
                </ul>
                <a href="https://nas.io/prologware-solutions-3" target="_blank" class="inline-block bg-primary-color text-white py-4 px-10 rounded-full text-lg font-semibold hover:bg-secondary-color transition duration-300 transform hover:scale-105 shadow-lg mt-10">
                    Join Our Community
                </a>
            </div>
        </div>
    </div>
</section>
<!-- cONTACT SECTION -->
<section id="contact" class="py-16 md:py-24">
    <div class="container mx-auto">
        <h2 class="section-heading text-center mb-12 md:mb-16" data-animate>Contact Me</h2>
        <div class="lg:flex lg:items-center lg:-mx-6">
            <div class="lg:w-1/2 lg:mx-6">
                <div class="relative bg-gradient-to-br from-[#232b47]/90 via-[#232b47]/80 to-[#181c2a]/80 rounded-3xl p-12 shadow-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
                    <div class="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-indigo-500/30 via-blue-500/20 to-purple-500/10 rounded-full blur-2xl opacity-40 -z-10"></div>
                    <div class="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tr from-purple-500/20 via-indigo-400/10 to-blue-500/10 rounded-full blur-2xl opacity-30 -z-10"></div>
                    <h1 class="text-4xl font-extrabold text-white capitalize lg:text-5xl mb-6 tracking-tight flex items-center gap-3">
                        <svg class="w-8 h-8 text-primary-color" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M21 16v-5a2 2 0 00-2-2h-1.26A8 8 0 103 15.25"></path></svg>
                        Let’s Work Together
                    </h1>
                    <p class="text-slate-200 text-lg md:text-xl font-medium leading-relaxed mb-8">Want to build something meaningful? Have a crazy idea to test? Need help with a project?</p>
                    <div class="space-y-6">
                        <div class="flex items-center gap-4">
                            <i class="fas fa-envelope text-2xl text-primary-color"></i>
                            <a href="mailto:<EMAIL>" class="text-lg md:text-xl font-semibold text-primary-color hover:underline transition"><EMAIL></a>
                        </div>
                        <div class="flex items-center gap-4">
                            <i class="fab fa-whatsapp text-3xl text-green-500"></i>
                            <a href="https://wa.me/923160360531" class="text-lg md:text-xl font-semibold text-green-400 hover:underline transition flex items-center gap-2"><span>+92 316 036-0531</span></a>
                        </div>
                        <div class="flex items-center gap-4">
                            <i class="fas fa-map-marker-alt text-2xl text-primary-color"></i>
                            <span class="text-lg md:text-xl font-semibold text-slate-200"> Based in Khairpur Mirs, Sindh – working globally</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-12 lg:mt-0 lg:w-1/2 lg:mx-6">
                <div class="card p-8">
                    <div class="relative bg-transparent rounded-none p-0 md:p-0 shadow-none backdrop-blur-none overflow-visible">
                        <h2 class="text-2xl md:text-3xl font-bold text-white mb-7 tracking-tight font-poppins flex items-center gap-2">
                            <svg class="w-6 h-6 text-primary-color" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M21 10.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l2.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6A8.38 8.38 0 0113.5 3a8.5 8.5 0 017.5 7.5z"></path></svg>
                            Send a Message
                        </h2>
                        <form id="contactForm" class="mt-6" action="https://api.web3forms.com/submit" method="POST">
                            <!-- Web3Forms Access Key (replace with your own key) -->
                            <input type="hidden" name="access_key" value="d4e644bf-1892-44d2-94d8-9947f958c9aa">
                            <!-- Optional: Redirect URL after submission -->
                            <input type="hidden" name="redirect" value="">
                            <!-- Optional: From Name -->
                            <input type="hidden" name="from_name" value="Abdul Basit Portfolio Contact">
                            <!-- Optional: Subject -->
                            <input type="hidden" name="subject" value="New Contact Message from Portfolio">
                            <div class="flex flex-col space-y-7">
                                <div>
                                    <label for="fullName" class="block text-base font-semibold text-primary-color mb-2 tracking-wide">Full Name</label>
                                    <input type="text" id="fullName" name="fullName" class="block w-full px-5 py-3 bg-gradient-to-r from-[#232b47]/60 to-[#181c2a]/60 border border-primary-color/30 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-primary-color focus:border-primary-color transition-all duration-300 shadow-inner" placeholder="Your Full Name" required>
                                </div>
                                <div>
                                    <label for="exampleInputEmail1" class="block text-base font-semibold text-primary-color mb-2 tracking-wide">Email Address</label>
                                    <input type="email" id="email" name="email" class="block w-full px-5 py-3 bg-gradient-to-r from-[#232b47]/60 to-[#181c2a]/60 border border-primary-color/30 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-primary-color focus:border-primary-color transition-all duration-300 shadow-inner" placeholder="Your Email Address" required>
                                </div>
                                <div>
                                    <label for="exampleFormControlSubject" class="block text-base font-semibold text-primary-color mb-2 tracking-wide">Subject</label>
                                    <input type="text" id="exampleFormControlSubject" name="subject" class="block w-full px-5 py-3 bg-gradient-to-r from-[#232b47]/60 to-[#181c2a]/60 border border-primary-color/30 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-primary-color focus:border-primary-color transition-all duration-300 shadow-inner" placeholder="Your Subject" required>
                                </div>
                                <div>
                                    <label for="exampleFormControlTextarea1" class="block text-base font-semibold text-primary-color mb-2 tracking-wide">Message</label>
                                    <textarea id="message" name="message" rows="4" class="block w-full px-5 py-3 bg-gradient-to-r from-[#232b47]/60 to-[#181c2a]/60 border border-primary-color/30 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-primary-color focus:border-primary-color transition-all duration-300 shadow-inner" placeholder="Your message here" required></textarea>
                                </div>
                                <button type="submit" class="relative premium-btn w-full py-4 px-8 text-lg font-semibold flex items-center justify-center gap-3 shadow-xl border border-primary-color/40 rounded-full bg-gradient-to-r from-[#232b47] via-[#181c2a] to-[#232b47] hover:bg-gradient-to-r hover:from-indigo-500 hover:via-blue-500 hover:to-purple-500 transition-all duration-300 group overflow-hidden">
                                    <span class="z-10 relative">Send Message</span>
                                    <span class="btn-glow"></span>
                                    <svg class="w-6 h-6 ml-2 text-primary-color group-hover:text-white transition" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M22 2L11 13"></path><path stroke-linecap="round" stroke-linejoin="round" d="M22 2l-7 20-4-9-9-4 20-7z"></path></svg>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Footer -->
 

  <!-- Unified Premium Footer Start -->
  <footer class="footer-premium mt-16 relative">
    <div class="footer-glow"></div>
    <div class="footer-content container mx-auto px-5 py-10 grid grid-cols-1 md:grid-cols-4 gap-10">
      <div>
        <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="flex flex-col items-center mb-4">
          <img src="./images/logo-removebg-preview.png"
               class="h-24 transition-transform transform hover:scale-110"
               alt="Abdul Basit Memon Logo - Professional Portfolio"
               loading="lazy"
               width="96"
               height="96" />
          <span class="mt-2 text-white font-semibold text-lg dark:text-white">Abdul Basit Memon</span>
        </a>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Visit Here</h2>
        <ul class="space-y-2">
          <li><a href="#services" target="_blank" class="footer-link">Services</a></li>
          <li><a href="#projects" target="_blank" class="footer-link">Projects</a></li>
          <li><a href="https://abdulbasitmemon.notion.site/d9cdb927bf1f48b6ad6abc54cf5b86a1?pvs=74" target="_blank" class="footer-link">My Notion</a></li>
        </ul>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Prologware Solutions</h2>
        <ul class="space-y-2">
          <li><a href="https://resources-heaven.super.site/" target="_blank" class="footer-link">Resources Heaven</a></li>
          <li><a href="https://chat.whatsapp.com/H4mF4unGvDI3vgCFMDl3Vs" target="_blank" class="footer-link">Internships & Jobs</a></li>
          <li><a href="https://xtiles.app/65a2a3cc83f7541b54f335f8" target="_blank" class="footer-link">Community</a></li>
        </ul>
      </div>
      <div>
        <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Additional Info</h2>
        <ul class="space-y-2">
          <li><a href="./community-guidelines.html" class="footer-link">Community Guideline</a></li>
          <li><a href="https://docs.google.com/forms/d/e/1FAIpQLSdKa4MB9goqYJYTbNhQCNE1GCjIBV43OazjQ2KU2CTXUHrNWQ/viewform" target="_blank" class="footer-link">Suggest &amp; Request</a></li>
          <li><a href="#" class="footer-link">Events</a></li>
        </ul>
      </div>
    </div>
    <hr class="footer-divider my-6 mx-auto w-11/12" />
    <div class="footer-content container mx-auto px-5 py-4 flex flex-col md:flex-row justify-between items-center">
      <span class="footer-copyright">© 2024 <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="footer-link">Abdul Basit MEMON™</a>. All Rights Reserved.</span>
      <div class="flex mt-4 md:mt-0">
          <a href="#" class="footer-social hover:text-white transition-colors duration-300" target="_blank">
        <i class="fab fa-facebook-f fa-lg"></i>
      </a>
      <a href="#" class="footer-social hover:text-white transition-colors duration-300" target="_blank">
        <i class="fab fa-twitter fa-lg"></i>
      </a>
      <a href="#" class="footer-social hover:text-white transition-colors duration-300" target="_blank">
        <i class="fab fa-instagram fa-lg"></i>
      </a>
      <a href="#" class="footer-social hover:text-white transition-colors duration-300" target="_blank">
        <i class="fab fa-linkedin-in fa-lg"></i>
      </a>
      <a href="#" class="footer-social hover:text-white transition-colors duration-300" target="_blank">
        <i class="fab fa-github fa-lg"></i>
      </a>
      </div>
    </div>
  </footer>
<!-- Here is down part -->
<!-- External Libraries -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.jsdelivr.net/npm/typed.js@2.0.12"></script>
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.4.5/flowbite.min.js"></script>
<!-- Custom JavaScript -->
<script src="./js/main.js"></script>
</body>
</html>