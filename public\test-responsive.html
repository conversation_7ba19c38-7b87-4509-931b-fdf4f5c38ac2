<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Design Test - <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .iframe-container {
            position: relative;
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }
        .device-label {
            background: #333;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        iframe {
            width: 100%;
            border: none;
            display: block;
        }
        .mobile { height: 667px; max-width: 375px; margin: 0 auto; }
        .tablet { height: 1024px; max-width: 768px; margin: 0 auto; }
        .desktop { height: 800px; }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .checklist {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .checklist h3 {
            margin-top: 0;
            color: #333;
        }
        
        .checklist ul {
            list-style-type: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .checklist li:before {
            content: "☐ ";
            color: #666;
            margin-right: 8px;
        }
        
        .performance-metrics {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Responsive Design Test - Abdul Basit Memon Portfolio</h1>
        <p>This page helps test the responsive design across different screen sizes.</p>
        
        <div class="test-grid">
            <div class="iframe-container mobile">
                <div class="device-label">Mobile (375px)</div>
                <iframe src="./index.html"></iframe>
            </div>
            
            <div class="iframe-container tablet">
                <div class="device-label">Tablet (768px)</div>
                <iframe src="./index.html"></iframe>
            </div>
        </div>
        
        <div class="iframe-container desktop">
            <div class="device-label">Desktop (1200px+)</div>
            <iframe src="./index.html"></iframe>
        </div>
        
        <div class="checklist">
            <h3>Responsive Design Checklist</h3>
            <ul>
                <li>Navigation menu works on mobile (hamburger menu)</li>
                <li>Text is readable on all screen sizes</li>
                <li>Images scale properly</li>
                <li>Buttons are touch-friendly (44px minimum)</li>
                <li>Content doesn't overflow horizontally</li>
                <li>Cards and sections stack properly on mobile</li>
                <li>Typography scales appropriately</li>
                <li>Interactive elements have proper hover/focus states</li>
                <li>Loading animations work smoothly</li>
                <li>Color contrast meets accessibility standards</li>
                <li>Page loads quickly on all devices</li>
                <li>Smooth scrolling works properly</li>
            </ul>
        </div>
        
        <div class="performance-metrics">
            <h3>Performance Metrics to Check</h3>
            <ul>
                <li><strong>First Contentful Paint (FCP):</strong> Should be under 1.8s</li>
                <li><strong>Largest Contentful Paint (LCP):</strong> Should be under 2.5s</li>
                <li><strong>Cumulative Layout Shift (CLS):</strong> Should be under 0.1</li>
                <li><strong>First Input Delay (FID):</strong> Should be under 100ms</li>
            </ul>
            <p><strong>Test Tools:</strong> Use Chrome DevTools, Lighthouse, or PageSpeed Insights</p>
        </div>
    </div>
    
    <script>
        // Add some basic responsive testing functionality
        function checkViewport() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            console.log(`Viewport: ${width}x${height}`);
            
            if (width < 640) {
                console.log('Mobile viewport detected');
            } else if (width < 1024) {
                console.log('Tablet viewport detected');
            } else {
                console.log('Desktop viewport detected');
            }
        }
        
        window.addEventListener('resize', checkViewport);
        checkViewport();
        
        // Test touch events
        if ('ontouchstart' in window) {
            console.log('Touch events supported');
        }
        
        // Test performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (performance.getEntriesByType) {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData) {
                        console.log('Performance metrics:', {
                            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                            firstPaint: performance.getEntriesByType('paint')[0]?.startTime
                        });
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
