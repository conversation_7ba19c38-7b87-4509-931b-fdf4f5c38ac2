<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graphic Design Services</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.1/dist/aos.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Nunito", sans-serif;
            font-optical-sizing: auto;
            font-style: normal;
        }
        
        .bg-radial-light {
            background: radial-gradient(circle, rgba(255,255,255,0.2) 10%, rgba(255,255,255,0) 80%);
        }
        @keyframes blob {
            0% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -50px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
            100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob {
            animation: blob 7s infinite;
        }
        .animation-delay-2000 {
            animation-delay: 2s;
        }
        .animation-delay-4000 {
            animation-delay: 4s;
        }
        @layer utilities {
            .nav-link:hover {
                @apply text-indigo-500 underline;
            }
        }
        .custom-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }
        @keyframes gradient-text {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .animate-text {
            background: linear-gradient(45deg, #FF6A3D, #F4DB7D);
            background-size: 200% 200%;
            animation: gradient-text 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .nav-link {
            color: #1A2238;
            transition: color 0.3s ease;
        }
        .nav-link:hover {
            color: #FF6A3D;
        }
        .header {
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }
        .header.scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .logo-scroll {
            display: flex;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        .logo-scroll::-webkit-scrollbar {
            display: none;
        }
        .logo-scroll > * {
            flex-shrink: 0;
            scroll-snap-align: start;
        }
        @media (min-width: 1024px) {
            .logo-scroll {
                justify-content: center;
            }
        }
        /* Additional styling for the footer */
    .text-primary {
        color: #9DAAF2; /* Adjust to your primary color */
    }
    .text-secondary {
        color: #FF6A3D; /* Adjust to your secondary color */
    }
    </style>
</head>

<body class="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
    <!-- Header and Nav -->
    <header class="header px-4 lg:px-6 h-16 lg:h-20 flex items-center bg-white fixed w-full top-0 z-50 shadow-md transition-all duration-300 ease-in-out">
        <div class="container mx-auto flex items-center justify-between">
            <a href="#Home" class="flex items-center h-full">
                <img src="./images/abm-design-ltd.png" alt="ABM Design Ltd Logo" class="h-32 w-32">
                <span class="sr-only">ABM Design</span>
            </a>
            <nav class="hidden lg:flex gap-6">
                <a href="#Services" class="text-sm font-medium nav-link font-semibold hover:text-blue-600 transition-colors duration-300">Services</a>
                <a href="#Additional-offers" class="text-sm font-medium nav-link font-semibold hover:text-blue-600 transition-colors duration-300">Additional Offerings</a>
                <a href="#Quality-Assurance" class="text-sm font-medium nav-link font-semibold hover:text-blue-600 transition-colors duration-300">Quality Assurance</a>
            </nav>
            <button id="menu-button" class="lg:hidden flex items-center justify-center p-2 rounded-md text-gray-700 hover:bg-gray-100 transition-all duration-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </header>
    
    <nav id="mobile-menu" class="fixed inset-0 bg-gray-900 bg-opacity-75 z-50 flex items-center justify-center hidden transition-opacity duration-300 ease-in-out">
        <div class="bg-white w-3/4 p-8 rounded-lg shadow-lg relative">
            <button id="close-menu" class="absolute top-4 right-4 text-gray-700 p-2 rounded-md hover:bg-gray-100 transition-all duration-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <ul class="space-y-4 text-center">
                <li><a href="#Services" class="text-lg font-medium nav-link hover:text-blue-600 transition-colors duration-300">Services</a></li>
                <li><a href="#Additional Offerings" class="text-lg font-medium nav-link hover:text-blue-600 transition-colors duration-300">Additional Offerings</a></li>
                <li><a href="#Quality-Assurance" class="text-lg font-medium nav-link hover:text-blue-600 transition-colors duration-300">Quality Assurance</a></li>
            </ul>
        </div>
    </nav>
    
    <!-- Spacer Div for header height -->
    <div class="h-16 lg:h-20"></div>

    <main class="flex-1">
        <!-- Hero -->
        <div id="Home"  class="relative overflow-hidden bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-blue-900 dark:via-purple-900 dark:to-pink-900">
            <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-16 lg:py-24 relative z-10">
                <div class="text-center">
                    <!-- Enhanced Heading -->
                    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-4 relative">
                        <span class="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-teal-500 to-blue-400 animate-text">The Artistic Edge</span>
                        <br class="hidden sm:block" />
                        <span class="relative inline-block text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-red-500 animate-text">Graphic Design Solutions</span>
                    </h1>
                    
                    <!-- Enhanced Paragraph -->
                    <p class="mt-3 text-base sm:text-lg md:text-xl font-semibold text-gray-600 dark:text-gray-300 relative before:absolute before:-inset-1 before:-skew-y-3 before:bg-gradient-to-r before:from-yellow-400 before:to-red-400 before:opacity-30 before:-z-10 before:rounded-lg dark:before:from-yellow-600 dark:before:to-red-600 after:absolute after:inset-0 after:bg-gradient-to-br after:from-blue-400 after:via-purple-400 after:to-pink-400 after:opacity-20 after:blur-lg after:rounded-lg after:-z-10 transition-transform transform hover:scale-105">
                        Step into a world of limitless creativity with 
                        <span class="relative font-semibold text-blue-600 dark:text-blue-400 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-teal-400 to-blue-600 animate-text">ABM DESIGN</span>.
                    </p>     
                    
                    <!-- Logos -->
                    <div class="mt-7 sm:mt-12 mx-auto max-w-xl relative">      
                        <!-- Enhanced "Trusted by:" Title -->
                        <p class="hidden sm:block text-sm font-semibold text-gray-500 dark:text-gray-400 mb-2 relative before:absolute before:-inset-1 before:-skew-y-3 before:bg-gradient-to-r before:from-yellow-400 before:to-red-400 before:opacity-30 before:-z-10 before:rounded-lg dark:before:from-yellow-600 dark:before:to-red-600 after:content-[''] after:absolute after:-inset-1 after:bg-gradient-to-r after:from-blue-400 after:via-teal-400 after:to-blue-600 after:opacity-40 after:-z-10 after:blur-md">
                            <span class="relative bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-teal-400 animate-text">Trusted by:</span>
                        </p>
                        <div class="logo-scroll flex gap-8 py-4">
                            <img src="./images/logos/GYeOAqtAo.png" alt="prologware Solutions" class="h-12 w-auto">
                            <img src="./images/logos/Digisked.png" alt="Digisked" class="h-12 w-auto">
                            <img src="./images/logos/railfood.pk.png" alt="RailFood.pk" class="h-12 w-auto">
                            <img src="./images/logos/Notion.png" alt="Notion" class="h-12 w-auto">
                            <img src="./images/logos/KBB.png" alt="KBB" class="h-12 w-auto">
                        </div>
                    </div>
                    
                    <!-- Enhanced Buttons -->
                    <div class="mt-5 flex flex-col sm:flex-row justify-center gap-3">
                        <a class="inline-flex justify-center items-center gap-x-3 text-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-indigo-600 hover:to-blue-600 border border-transparent text-white text-sm font-medium rounded-full focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 focus:ring-offset-white py-3 px-6 dark:focus:ring-offset-gray-800 transition-transform transform hover:scale-105 shadow-lg" href="#">
                            Get a Quote
                            <svg class="w-3 h-3" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M5.27921 2L10.9257 7.64645C11.1209 7.84171 11.1209 8.15829 10.9257 8.35355L5.27921 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </a>
                        <a class="relative group inline-flex justify-center items-center gap-x-3.5 text-center bg-white border hover:border-gray-300 shadow-lg text-sm font-medium rounded-full text-gray-600 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white transition-transform transform hover:scale-105 py-3 px-6 dark:bg-slate-900 dark:border-gray-800 dark:hover:border-gray-600 dark:text-gray-400 dark:hover:text-white dark:focus:ring-gray-700 dark:focus:ring-offset-gray-800" href="#">
                            View Portfolio
                        </a>
                    </div>
                </div>
            </div>
    
            <!-- Decorative SVG shapes -->
            <div class="absolute top-0 left-0 w-40 h-40 md:w-96 md:h-96 bg-purple-100 rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-blob dark:bg-purple-900"></div>
            <div class="absolute top-0 right-0 w-40 h-40 md:w-96 md:h-96 bg-yellow-100 rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-blob animation-delay-2000 dark:bg-yellow-900"></div>
            <div class="absolute bottom-0 left-1/3 w-40 h-40 md:w-96 md:h-96 bg-pink-100 rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-blob animation-delay-4000 dark:bg-pink-900"></div>
    
            <!-- Ray light effect -->
            <div class="absolute top-0 left-0 w-full h-full pointer-events-none">
                <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/20 to-transparent mix-blend-screen pointer-events-none"></div>
                <div class="absolute top-0 right-0 w-full h-full bg-gradient-to-bl from-transparent via-white/20 to-transparent mix-blend-screen pointer-events-none"></div>
                <div class="absolute bottom-0 left-0 w-full h-full bg-gradient-to-tr from-transparent via-white/20 to-transparent mix-blend-screen pointer-events-none"></div>
                <div class="absolute bottom-0 right-0 w-full h-full bg-gradient-to-tl from-transparent via-white/20 to-transparent mix-blend-screen pointer-events-none"></div>
            </div>
        </div>
        <!-- End Hero -->
        
        <!--Section #B-->
        <section class="w-full py-12 md:py-24 lg:py-32" id="Services" data-aos="fade-up">
            <div class="container space-y-12 px-4 md:px-6">
                <div class="flex flex-col items-center justify-center space-y-4 text-center">
                    <div class="space-y-2">
                        <div class="inline-block rounded-lg bg-gray-200 px-3 py-1 text-sm">Services Offered</div>
                        <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl">Elevate Your Brand with Our Expertise</h2>
                        <p class="max-w-[900px] text-gray-500 md:text-xl lg:text-base xl:text-xl">
                            From logo design to social media graphics, we offer a wide range of graphic design services to help you stand out.
                        </p>
                    </div>
                </div>
                <div class="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-lg font-bold">Logo Design (Branding)</h3>
                        <p class="text-sm text-gray-500">Create a unique and memorable logo that reflects your brand identity.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-lg font-bold">Poster Design</h3>
                        <p class="text-sm text-gray-500">Grab attention with eye-catching poster designs for your events or campaigns.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-lg font-bold">Brochure Designs</h3>
                        <p class="text-sm text-gray-500">Create professional and informative brochures to showcase your products or services.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="400">
                        <h3 class="text-lg font-bold">Social Media Posts</h3>
                        <p class="text-sm text-gray-500">Engage your audience with visually appealing social media graphics.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="500">
                        <h3 class="text-lg font-bold">Banner Designs</h3>
                        <p class="text-sm text-gray-500">Develop eye-catching banner designs for various platforms like Twitter, YouTube, LinkedIn, and Facebook.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="600">
                        <h3 class="text-lg font-bold">Thumbnails Design</h3>
                        <p class="text-sm text-gray-500">Create visually appealing thumbnails to enhance the visibility of your content.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="700">
                        <h3 class="text-lg font-bold">Business-related Designs</h3>
                        <p class="text-sm text-gray-500">Elevate your business with professional designs for stationery, invoices, and other business materials.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="800">
                        <h3 class="text-lg font-bold">Icons, Illustrations, and Art</h3>
                        <p class="text-sm text-gray-500">Enhance your designs with custom-made icons, illustrations, and artwork.</p>
                    </div>
                </div>
            </div>
        </section>
        
<!-- Section #C: Additional Offerings -->
<section class="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-gray-100 via-gray-200 to-gray-300" id="Additional-offers" data-aos="fade-up">
    <div class="container mx-auto space-y-12 px-4 md:px-6">
        <div class="flex flex-col items-center justify-center space-y-4 text-center">
            <div class="space-y-2">
                <!-- Enhanced Tag -->
                <div class="inline-block rounded-lg bg-gray-200 px-3 py-1 text-sm">Additional Offerings</div>
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-gray-800 dark:text-white mb-4">
                    Elevate Your Brand with Our Expanded Services
                </h2>
                <p class="max-w-[900px] mx-auto text-gray-600 md:text-lg lg:text-xl dark:text-gray-300">
                    In addition to our core graphic design services, we offer a range of complementary offerings to enhance your brand.
                </p>
            </div>
        </div>
        <div class="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
            <!-- Card 1 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="100">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Logo Animations</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Bring your logo to life with animated effects that captivate your audience.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 2 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="200">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Motion Graphics</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Enhance your brand's storytelling with dynamic motion graphics and animations.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 3 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="300">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">3D/2D AutoCAD Models</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Visualize your products or concepts with detailed 3D/2D AutoCAD models.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 4 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="400">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Video Editing</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Craft compelling videos that engage and inspire your audience.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 5 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="500">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">PowerPoint Design</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Captivate with custom PowerPoint designs with dynamic animations.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 6 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="600">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">UI/UX Design</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Create intuitive and user-friendly interfaces that enhance user experience.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 7 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="700">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Notion Template Design</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Boost productivity with our stunning, custom Notion templates that streamline workflows.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
            <!-- Card 8 -->
            <div class="group relative grid gap-4 p-6 bg-white rounded-lg shadow-lg transition-transform transform hover:scale-105" data-aos="fade-up" data-aos-delay="800">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Custom Document Designs</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Elevate your brand with our striking custom documents that fuse sleek design with powerful functionality.</p>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-200 via-teal-200 to-blue-300 opacity-20 blur-md group-hover:opacity-30 transition-opacity"></div>
            </div>
        </div>
    </div>
</section>
        <!--Section #D-->
        <section class="w-full py-12 md:py-24 lg:py-32" id="Quality-Assurance" data-aos="fade-up">
            <div class="container space-y-12 px-4 md:px-6">
                <div class="flex flex-col items-center justify-center space-y-4 text-center">
                    <div class="space-y-2">
                        <div class="inline-block rounded-lg bg-gray-200 px-3 py-1 text-sm">Quality Assurance</div>
                        <h2 class="text-3xl font-bold tracking-tighter sm:text-5xl">Quality is Our Priority</h2>
                        <p class="max-w-[900px] text-gray-500 md:text-xl lg:text-base xl:text-xl">
                            We ensure the highest quality in every project, meeting your expectations and exceeding industry standards.
                        </p>
                    </div>
                </div>
                <div class="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-lg font-bold">Expert Team</h3>
                        <p class="text-sm text-gray-500">Our dedicated team of graphic designers and creatives bring expertise and passion to every project.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="200">
                        <h3 class="text-lg font-bold">Creative Excellence</h3>
                        <p class="text-sm text-gray-500">We strive for creative excellence, pushing boundaries to deliver innovative and impactful designs.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="300">
                        <h3 class="text-lg font-bold">Client Satisfaction</h3>
                        <p class="text-sm text-gray-500">Your satisfaction is our priority. We listen, adapt, and deliver to meet your specific needs and preferences.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="400">
                        <h3 class="text-lg font-bold">Quality Control</h3>
                        <p class="text-sm text-gray-500">We maintain rigorous quality control processes to ensure flawless execution and consistent delivery.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="500">
                        <h3 class="text-lg font-bold">Industry Standards</h3>
                        <p class="text-sm text-gray-500">Adhering to industry standards, we guarantee designs that are not only aesthetically pleasing but also functionally effective.</p>
                    </div>
                    <div class="grid gap-1" data-aos="fade-up" data-aos-delay="600">
                        <h3 class="text-lg font-bold">Continuous Improvement</h3>
                        <p class="text-sm text-gray-500">We continually improve our processes and skills to stay ahead in an ever-evolving design landscape.</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="w-full bg-gray-900 text-white py-10 relative">
            <div class="container px-4 mx-auto flex flex-col md:flex-row justify-between items-center space-y-8 md:space-y-0 relative z-10">
              <div class="text-center md:text-left">
                <h3 class="text-xl font-bold text-teal-400 mb-2">ABM Design</h3>
                <p class="text-sm text-gray-400">Creative solutions for modern brands.</p>
              </div>
              <nav class="flex flex-col md:flex-row md:space-x-6">
                <a href="#" class="text-sm hover:text-teal-400 transition-transform transform hover:scale-105 hover:underline">Services</a>
                <a href="#" class="text-sm hover:text-teal-400 transition-transform transform hover:scale-105 hover:underline">Offerings</a>
                <a href="#" class="text-sm hover:text-teal-400 transition-transform transform hover:scale-105 hover:underline">Quality</a>
              </nav>
              <div class="text-sm text-gray-400 text-center md:text-right">&copy; 2024 ABM Design. All rights reserved.</div>
            </div>
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 opacity-20 z-0 pointer-events-none"></div>
            <div class="fixed bottom-0 right-0 mb-4 mr-4">
              <button class="bg-teal-500 text-white p-2 rounded-full shadow-lg hover:bg-teal-600 transition duration-300 transform hover:scale-110">
                <i class="fas fa-arrow-up"></i>
              </button>
            </div>
          </footer>
                                                  
    </main>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.1/dist/aos.js"></script>
    <script>
        const menuButton = document.getElementById('menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const closeMenu = document.getElementById('close-menu');

        menuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
        });

        closeMenu.addEventListener('click', () => {
        mobileMenu.classList.add('hidden');
        });

        window.addEventListener('click', (e) => {
        if (e.target === mobileMenu) {
        mobileMenu.classList.add('hidden');
        }
        });

window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 50) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
});

AOS.init({
    duration: 1000,
    once: true,
});

// Logo scroll animation
const logoScroll = document.querySelector('.logo-scroll');
let isDown = false;
let startX;
let scrollLeft;

logoScroll.addEventListener('mousedown', (e) => {
    isDown = true;
    logoScroll.classList.add('active');
    startX = e.pageX - logoScroll.offsetLeft;
    scrollLeft = logoScroll.scrollLeft;
});

logoScroll.addEventListener('mouseleave', () => {
    isDown = false;
    logoScroll.classList.remove('active');
});

logoScroll.addEventListener('mouseup', () => {
    isDown = false;
    logoScroll.classList.remove('active');
});

logoScroll.addEventListener('mousemove', (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - logoScroll.offsetLeft;
    const walk = (x - startX) * 3; // Adjust scroll speed by changing multiplier
    logoScroll.scrollLeft = scrollLeft - walk;
});

</script>
</body>
</html>