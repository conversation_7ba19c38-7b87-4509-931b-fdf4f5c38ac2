<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Techdio - Advanced Learning Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/2.8.2/alpine.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'text': '#2c3e50;',
                        'primary': '#7658b0',
                        'secondary': '#e0e0e0',
                        'accent': '#ff8c42',
                    },
                    fontFamily: {
                        'body': ['Poppins', 'sans-serif'],
                        'sans': ['Poppins', 'sans-serif']
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --text-color: #2c3e50;
            --primary-color: #7658b0;
            --secondary-color: #e0e0e0;
            --accent-color: #ff8c42;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        @keyframes text-glow {
    0%, 100% {
        text-shadow: 0 0 8px rgba(255, 140, 66, 0.7), 0 0 16px rgba(255, 140, 66, 0.5), 0 0 24px rgba(118, 88, 176, 0.3);
    }
    50% {
        text-shadow: 0 0 8px rgba(118, 88, 176, 0.7), 0 0 16px rgba(118, 88, 176, 0.5), 0 0 24px rgba(255, 140, 66, 0.3);
    }
}

.animate-text-glow {
    animation: text-glow 2s ease-in-out infinite alternate;
}

@keyframes scroll {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animate-scroll {
    animation: scroll 5s linear infinite;
}

</style>
</head>
<body class="font-body">
<!-- Header -->
<header class="fixed w-full z-50 text-[#2c3e50] bg-gradient-to-r from-[#e0e0e0] via-[#f5f5f5] to-[#dcdcdc] shadow-lg">
    <div x-data="{ open: false }" class="container mx-auto flex flex-col md:flex-row md:items-center md:justify-between px-4 py-4">
        <div class="flex items-center justify-between">
            <a href="#Home" class="text-2xl font-bold tracking-widest text-[#ff8c42] uppercase hover:text-[#7658b0] transition-colors duration-300">Techdio</a>
            <button class="md:hidden focus:outline-none" @click="open = !open">
                <svg fill="currentColor" viewBox="0 0 20 20" class="w-6 h-6 text-[#ff8c42] hover:text-[#7658b0] transition-colors duration-300">
                    <!-- SVG Paths -->
                    <path fill-rule="evenodd" d="M3 5h14a1 1 0 110 2H3a1 1 0 110-2zm0 4h14a1 1 0 110 2H3a1 1 0 110-2zm0 4h14a1 1 0 110 2H3a1 1 0 110-2z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>
        <nav :class="{'flex': open, 'hidden': !open}" class="flex-col flex-grow hidden md:flex md:flex-row md:justify-end mt-4 md:mt-0 space-y-2 md:space-y-0">
            <a class="px-4 py-2 mt-2 text-sm font-semibold rounded-lg md:mt-0 md:ml-4 text-[#2c3e50] hover:text-[#ff8c42] hover:bg-[#7658b0] focus:bg-[#7658b0] focus:text-[#e0e0e0] transition-colors duration-300" href="#Home">Home</a>
            <a class="px-4 py-2 mt-2 text-sm font-semibold rounded-lg md:mt-0 md:ml-4 text-[#2c3e50] hover:text-[#ff8c42] hover:bg-[#7658b0] focus:bg-[#7658b0] focus:text-[#e0e0e0] transition-colors duration-300" href="#categories">Categories</a>
            <a class="px-4 py-2 mt-2 text-sm font-semibold rounded-lg md:mt-0 md:ml-4 text-[#2c3e50] hover:text-[#ff8c42] hover:bg-[#7658b0] focus:bg-[#7658b0] focus:text-[#e0e0e0] transition-colors duration-300" href="#features">Features</a>
            <a class="px-4 py-2 mt-2 text-sm font-semibold rounded-lg md:mt-0 md:ml-4 text-[#2c3e50] hover:text-[#ff8c42] hover:bg-[#7658b0] focus:bg-[#7658b0] focus:text-[#e0e0e0] transition-colors duration-300" href="#testimonials">Testimonials</a>
        </nav>
    </div>
</header>    
<!--Home/ Hero Section-->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-[#2c3e50] via-[#7658b0] to-[#2c3e50]" id="Home">
        <div class="absolute inset-0 bg-[#ff8c42] opacity-30 mix-blend-multiply animate-pulse"></div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-4 text-[#e0e0e0] drop-shadow-md">
                Your Gateway to <span class="bg-clip-text text-transparent bg-gradient-to-r from-[#ff8c42] via-[#7658b0] to-[#ff8c42] animate-text-glow">Boundless Learning</span> and <span class="bg-clip-text text-transparent bg-gradient-to-r from-[#7658b0] via-[#ff8c42] to-[#7658b0] animate-text-glow">Innovation</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-[#e0e0e0] leading-relaxed drop-shadow-sm">
                Unlock the future of education with cutting-edge courses and revolutionary learning experiences
            </p>
            <div class="flex justify-center space-x-4">
                <a href="#" class="bg-[#ff8c42] text-[#2c3e50] px-8 py-4 rounded-full font-semibold shadow-lg transform transition duration-300 hover:scale-105 hover:bg-[#7658b0] hover:text-[#e0e0e0] hover:shadow-2xl">
                    Explore Courses
                </a>
                <a href="#" class="bg-transparent border-2 border-[#ff8c42] text-[#ff8c42] px-8 py-4 rounded-full font-semibold shadow-lg transform transition duration-300 hover:scale-105 hover:bg-[#ff8c42] hover:text-[#2c3e50] hover:shadow-2xl">
                    Get Started
                </a>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-[#ff8c42] via-[#7658b0] to-[#ff8c42] animate-scroll"></div>
    </section>

<!--Categories section -->
<section id="categories" class="py-20 bg-gray-50">
    <div class="container mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12 gradient-text">Our Learning Categories</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div class="bg-white rounded-lg shadow-lg overflow-hidden transform transition duration-500 hover:scale-105 card-hover">
                <img src="./images/tech-courses.jpg" alt="Tech" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-2xl font-bold mb-4 text-primary-600">Tech</h3>
                    <p class="text-gray-700">Dive into cutting-edge technology courses and master the digital realm. From coding to AI, we've got you covered.</p>
                    <div class="card-hover-content mt-4">
                        <a href="https://courses.so/techdeo-TECH_courses" target="_blank" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">Learn More →</a>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden transform transition duration-500 hover:scale-105 card-hover">
                <img src="./images/design-courses.jpg" alt="Design" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-2xl font-bold mb-4 text-primary-600">Design</h3>
                    <p class="text-gray-700">Unleash your creativity with our comprehensive design courses. Learn UI/UX, graphic design, and more.</p>
                    <div class="card-hover-content mt-4">
                        <a href="https://techdio-design.bullet.site/" target="_blank" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">Learn More →</a>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden transform transition duration-500 hover:scale-105 card-hover">
                <img src="/public/pages/images/islam - tution.png" alt="Islam" class="w-full h-48 object-cover">
                <div class="p-6">
                    <h3 class="text-2xl font-bold mb-4 text-primary-600">Islam</h3>
                    <p class="text-gray-700">Deepen your understanding of Islamic studies with our diverse courses. Explore history, theology, and contemporary issues.</p>
                    <div class="card-hover-content mt-4">
                        <a href="https://techdeo-islam.oopy.io/" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">Learn More →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Features Section -->
<section id="features" class="py-20 bg-gradient-to-r from-[#f8fafc] via-[#eef2f7] to-[#e3e9f2]">
    <div class="container mx-auto px-6">
        <h2 class="text-4xl font-bold text-center mb-12 text-[#2c3e50]">Our Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div class="bg-white rounded-xl shadow-lg p-8 transform transition-transform duration-500 hover:scale-105 hover:shadow-2xl">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-book-open text-5xl text-[#ff8c42]"></i>
                </div>
                <h3 class="text-2xl font-semibold text-center mb-4 text-[#7658b0]">Free Courses</h3>
                <p class="text-[#2c3e50] text-center leading-relaxed">
                    Access a vast library of high-quality courses across Tech, Design, and Islamic studies at absolutely no cost.
                </p>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-8 transform transition-transform duration-500 hover:scale-105 hover:shadow-2xl">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-chalkboard-teacher text-5xl text-[#ff8c42]"></i>
                </div>
                <h3 class="text-2xl font-semibold text-center mb-4 text-[#7658b0]">Personalized Tutoring</h3>
                <p class="text-[#2c3e50] text-center leading-relaxed mb-6">
                    Get one-on-one support from our expert tutors. Tailored guidance to help you overcome challenges.
                </p>
                <div class="flex justify-center">
                    <a href="#" class="px-6 py-2 bg-[#ff8c42] text-white rounded-full shadow hover:bg-[#7658b0] hover:text-[#eef2f7] transition-all duration-300">Learn More</a>
                </div>
            </div>
            <div class="bg-white rounded-xl shadow-lg p-8 transform transition-transform duration-500 hover:scale-105 hover:shadow-2xl">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-cogs text-5xl text-[#ff8c42]"></i>
                </div>
                <h3 class="text-2xl font-semibold text-center mb-4 text-[#7658b0]">Custom Course Development</h3>
                <p class="text-[#2c3e50] text-center leading-relaxed mb-6">
                    Shape your unique learning path with our custom course creation service tailored to your needs.
                </p>
                <div class="flex justify-center">
                    <a href="#" class="px-6 py-2 bg-[#ff8c42] text-white rounded-full shadow hover:bg-[#7658b0] hover:text-[#eef2f7] transition-all duration-300">Get Started</a>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Testimonials Section -->
<section id="testimonials" class="py-20 bg-background">
    <div class="container mx-auto px-6">
        <h2 class="text-3xl font-bold text-center mb-12 gradient-text">What Our Learners Say</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div class="bg-text rounded-lg shadow-lg p-6 transform transition duration-500 hover:scale-105">
                <p class="text-accent mb-6">"Techdio has transformed my learning experience. The courses are engaging, and the personalized tutoring has helped me achieve my goals faster than I ever imagined."</p>
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Sarah Johnson" class="w-12 h-12 rounded-full mr-4">
                    <div>
                        <h4 class="font-bold text-primary">Sarah Johnson</h4>
                        <p class="text-secondary">Web Developer</p>
                    </div>
                </div>
            </div>
            <div class="bg-text rounded-lg shadow-lg p-6 transform transition duration-500 hover:scale-105">
                <p class="text-accent mb-6">"The depth of knowledge in the Islamic studies courses is impressive. Techdio has helped me gain a more comprehensive understanding of my faith."</p>
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Ahmed Hassan" class="w-12 h-12 rounded-full mr-4">
                    <div>
                        <h4 class="font-bold text-primary">Ahmed Hassan</h4>
                        <p class="text-secondary">Islamic Studies Enthusiast</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-gradient-to-r from-primary to-secondary text-background py-12">
    <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Brand Section -->
            <div class="mb-8 md:mb-0">
                <h2 class="text-3xl font-bold mb-2">Techdio</h2>
                <p class="text-accent">Empowering Learners Worldwide</p>
                <p class="text-text mt-2 max-w-xs leading-relaxed">
                    Your gateway to high-quality learning resources and personalized educational experiences tailored to help you succeed.
                </p>
            </div>

            <!-- Navigation Links -->
            <div>
                <h3 class="text-xl font-semibold mb-4 text-accent">Explore</h3>
                <ul class="space-y-2">
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Courses</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Tutoring</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Blog</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">About Us</a></li>
                </ul>
            </div>

            <div>
                <h3 class="text-xl font-semibold mb-4 text-accent">Resources</h3>
                <ul class="space-y-2">
                    <li><a href="#" class="hover:text-text transition-colors duration-300">FAQ</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Support</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Community</a></li>
                    <li><a href="#" class="hover:text-text transition-colors duration-300">Privacy Policy</a></li>
                </ul>
            </div>

            <!-- Contact Information -->
            <div>
                <h3 class="text-xl font-semibold mb-4 text-accent">Contact Us</h3>
                <p class="text-text leading-relaxed mb-4">
                    123 Tech Street, Learning City, TE 45678
                </p>
                <p class="mb-2">
                    <a href="mailto:<EMAIL>" class="hover:text-accent transition-colors duration-300">
                        <i class="fas fa-envelope mr-2"></i><EMAIL>
                    </a>
                </p>
                <p>
                    <a href="tel:+1234567890" class="hover:text-accent transition-colors duration-300">
                        <i class="fas fa-phone mr-2"></i>****** 567 890
                    </a>
                </p>
            </div>
        </div>

        <hr class="border-accent my-8">

        <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-center text-text mb-4 md:mb-0">&copy; 2024 Techdio. All rights reserved.</p>
            
            <!-- Social Media Links -->
            <div class="flex space-x-4">
                <a href="#" class="hover:text-accent transition-colors duration-300">
                    <i class="fab fa-facebook text-2xl"></i>
                </a>
                <a href="#" class="hover:text-accent transition-colors duration-300">
                    <i class="fab fa-twitter text-2xl"></i>
                </a>
                <a href="#" class="hover:text-accent transition-colors duration-300">
                    <i class="fab fa-instagram text-2xl"></i>
                </a>
                <a href="#" class="hover:text-accent transition-colors duration-300">
                    <i class="fab fa-linkedin text-2xl"></i>
                </a>
            </div>
        </div>
    </div>
</footer>

<script>
    function toggleMenu() {
        const menuIcon = document.querySelector('.menu-icon');
        const closeIcon = document.querySelector('.close-icon');
        const navLinks = document.querySelector('.nav-links');
        
        menuIcon.classList.toggle('hidden');
        closeIcon.classList.toggle('hidden');
        navLinks.classList.toggle('active');
    }
</script>
</body>
</html>