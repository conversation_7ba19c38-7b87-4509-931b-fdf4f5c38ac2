<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prologware Solutions Community Guidelines</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <link href="https://unpkg.com/flowbite@1.6.5/dist/flowbite.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@material-tailwind/react@latest/dist/tailwind.min.css" />
    <!-- Material Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">

    <style>
        :root {
            --primary-color: #1d4ed8; /* Primary color */
            --secondary-color: #374151; /* Secondary color */
            --accent-color: #e11d48; /* Accent color */
            --text-color: #1f2937; /* Text color */
            --bg-color: #f9fafb; /* Background color */
        }

        body {
            font-family: 'Roboto', sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        header {
            text-align: center;
            margin-bottom: 3rem;
        }

        h1 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .subtitle {
            font-size: 1.2rem;
            color: var(--secondary-color);
        }

        section {
            margin-bottom: 3rem;
        }

        h2 {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
        }

        .highlight {
            background-color: rgba(225, 29, 72, 0.1);
            padding: 0.5rem;
            border-radius: 5px;
            font-weight: bold;
            display: block;
            margin-bottom: 1rem;
        }

        .accordion {
            @apply bg-primary text-white cursor-pointer py-3 px-5 w-full text-left rounded-md;
            font-size: 1rem;
            transition: 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .accordion:hover, .accordion.active {
            @apply bg-indigo-700;
        }

        .accordion::after {
            content: '\002B';
            font-weight: bold;
            margin-left: 5px;
        }

        .accordion.active::after {
            content: "\2212";
        }

        .content {
            @apply py-4 px-6;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
            background-color: white;
            border-radius: 0 0 5px 5px;
            border: 1px solid var(--primary-color);
        }

        .content ul {
            padding-left: 1rem;
            margin-bottom: 0;
        }

        .content li {
            margin-bottom: 0.75rem;
            position: relative;
            padding-left: 1.5rem;
            font-size: 1rem;
        }

        .content li::before {
            content: "\f00c";
            font-family: "Boxicons";
            font-weight: 900;
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-size: 1.25rem;
        }

        @media (max-width: 600px) {
            .container {
                padding: 1rem;
            }

            h1 {
                font-size: 2rem;
            }

            h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="flex items-center justify-center gap-4 mb-12">
            <!-- Logo -->
            <img src="/public/pages/images/logos/logo of PLW.png" alt="Prologware Solutions Logo" class="w-16 h-16 object-contain">
        
            <!-- Title and Subtitle -->
            <div class="text-center">
                <h1 class="text-3xl font-bold text-primary">Prologware Solutions Community Guidelines</h1>
                <p class="subtitle text-lg text-secondary">"To Learn, Grow, Earn & Collab"</p>
            </div>
        </header>
        

     <!-- Purpose Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">Purpose</h2>
    <p class="text-lg text-gray-700 mb-4">Welcome to Prologware Solutions! Our community is dedicated to fostering a collaborative environment where individuals can discover, grow, succeed, and connect. We aim to be a vibrant hub for learners, creators, and innovators.</p>
    
    <ul class="space-y-4">
        <!-- Item 1 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 text-blue-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-user text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">This community is for professionals and enthusiasts in technology, designing, and entrepreneurship.</span>
        </li>
        
        <!-- Item 2 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 text-green-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-graduation text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">We welcome individuals of all experience levels who are passionate about learning and sharing knowledge.</span>
        </li>
        
        <!-- Item 3 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-network-chart text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Our community helps members overcome challenges in their career growth, skill development, and professional networking.</span>
        </li>
    </ul>
</section>
<section class="max-w-4xl mx-auto p-6 bg-white rounded-md">
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Community Guidelines</h2>
    <p class="text-gray-700 mb-6">
        <span class="font-semibold text-blue-600 bg-blue-100 p-1 rounded">If you read only one rule, read this:</span> Be kind and respectful to all community members.
    </p>

    <!-- Collapsible Sections -->
    <div class="space-y-4">
        <!-- Encouraged Behaviors -->
        <div class="border-t border-gray-200">
            <button class="flex items-center justify-between w-full p-4 text-left text-gray-900 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleSection('section1')">
                <div class="flex items-center gap-2">
                    <span class="material-icons text-blue-500">thumb_up</span>
                    <span class="text-lg font-semibold">Encouraged Behaviors</span>
                </div>
            </button>
            <div id="section1" class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                <ul class="list-disc text-gray-700 pl-6 space-y-2">
                    <li>Share thoughts and experiences related to technology, digital marketing, and entrepreneurship.</li>
                    <li>Engage in constructive discussions and debates.</li>
                    <li>Offer support and advice to fellow members.</li>
                    <li>Participate in webinars, seminars, and career-building sessions.</li>
                </ul>
            </div>
        </div>

        <!-- Unacceptable Behaviors -->
        <div class="border-t border-gray-200">
            <button class="flex items-center justify-between w-full p-4 text-left text-gray-900 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleSection('section2')">
                <div class="flex items-center gap-2">
                    <span class="material-icons text-red-500">thumb_down</span>
                    <span class="text-lg font-semibold">Unacceptable Behaviors</span>
                </div>
            </button>
            <div id="section2" class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                <ul class="list-disc text-gray-700 pl-6 space-y-2">
                    <li>No unsolicited selling or soliciting.</li>
                    <li>No sharing of unauthorized content or violation of copyrights.</li>
                    <li>No offensive, discriminatory, or threatening content.</li>
                    <li>No spamming or repetitive promotional content.</li>
                    <li>No sharing of false information or misleading content.</li>
                </ul>
            </div>
        </div>

        <!-- Privacy and Confidentiality -->
        <div class="border-t border-gray-200">
            <button class="flex items-center justify-between w-full p-4 text-left text-gray-900 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleSection('section3')">
                <div class="flex items-center gap-2">
                    <span class="material-icons text-green-500">privacy_tip</span>
                    <span class="text-lg font-semibold">Privacy and Confidentiality</span>
                </div>
            </button>
            <div id="section3" class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                <ul class="list-disc text-gray-700 pl-6 space-y-2">
                    <li>Respect the Chatham House Rule in all discussions.</li>
                    <li>Do not share private information about others without their explicit permission.</li>
                </ul>
            </div>
        </div>

        <!-- Moderation -->
        <div class="border-t border-gray-200">
            <button class="flex items-center justify-between w-full p-4 text-left text-gray-900 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleSection('section4')">
                <div class="flex items-center gap-2">
                    <span class="material-icons text-yellow-500">policy</span>
                    <span class="text-lg font-semibold">Moderation</span>
                </div>
            </button>
            <div id="section4" class="max-h-0 overflow-hidden transition-all duration-300 ease-in-out">
                <ul class="list-disc text-gray-700 pl-6 space-y-2">
                    <li>The host's decision on content moderation and member removal is final.</li>
                    <li>Posts violating guidelines may be removed with or without warning.</li>
                    <li>Members may receive warnings, suspensions, or be removed for repeated violations.</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- JavaScript for Toggle Functionality -->

       <!-- Resources Heaven Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">Resources Heaven</h2>
    <p class="text-lg text-gray-700 mb-4">Explore our wealth of resources designed to enhance your skills and knowledge.</p>
    
    <ul class="space-y-4">
        <!-- Item 1 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 text-blue-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-cog text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Utilize premium courses, tools, and software.</span>
        </li>
        
        <!-- Item 2 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 text-green-500 transition-transform transform hover:scale-110">
                <i class='bx bx-refresh text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Suggest and request resources to enrich our community.</span>
        </li>
        
        <!-- Item 3 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-red-100 text-red-500 transition-transform transform hover:scale-110">
                <i class='bx bx-no-entry text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Respect licensing and copyrights.</span>
        </li>
    </ul>
    
    <a href="https://resources-heaven.super.site/" class="inline-flex items-center px-4 py-2 text-white bg-blue-500 border border-transparent rounded-md shadow-sm text-sm font-medium hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mt-6" target="_blank" rel="noopener noreferrer">
        <i class='bx bx-link-external'></i> Access Resources Heaven
    </a>
</section>

      <!-- Scholarships & Jobs Updates Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">Scholarships & Jobs Updates</h2>
    <p class="text-lg text-gray-700 mb-4">Stay informed about the latest scholarships, internships, and job opportunities available to community members.</p>
    
    <ul class="space-y-4">
        <!-- Item 1 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 text-blue-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-graduation text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Regular updates on scholarships for students and professionals.</span>
        </li>
        
        <!-- Item 2 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 text-green-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-briefcase text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Access to job openings and internship opportunities in relevant fields.</span>
        </li>
        
        <!-- Item 3 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-500 transition-transform transform hover:scale-110">
                <i class='bx bx-check-circle text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Tips and guidance on applying for these opportunities.</span>
        </li>
    </ul>
</section>


       <!-- Chit Chat Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">Chit Chat</h2>
    <p class="text-lg text-gray-700 mb-4">Engage in open discussions, network with peers, and share your experiences in our Chit Chat section.</p>
    
    <ul class="space-y-4">
        <!-- Item 1 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-purple-100 text-purple-500 transition-transform transform hover:scale-110">
                <i class='bx bx-conversation text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Participate in casual conversations about tech, career growth, and more.</span>
        </li>
        
        <!-- Item 2 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 text-green-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-network-chart text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Connect with like-minded individuals.</span>
        </li>
        
        <!-- Item 3 -->
        <li class="flex items-center gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-red-100 text-red-500 transition-transform transform hover:scale-110">
                <i class='bx bxs-heart-circle text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Build meaningful relationships within the community.</span>
        </li>
    </ul>
</section>
<!-- How to Access Resources Heaven Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">How to Access Resources Heaven</h2>
    <p class="text-lg text-gray-700 mb-4">Follow these simple steps to access the Resources Heaven site:</p>
    
    <div class="space-y-6">
        <!-- Step 1 -->
        <div class="flex items-start gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 text-blue-500">
                <i class='bx bx-link text-2xl'></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-primary mb-1">Click the Button</h3>
                <p class="text-gray-800">Just click on the 'Go to Resources Heaven' button below to be redirected to the Resources Heaven site.</p>
            </div>
        </div>

        <!-- Step 2 -->
        <div class="flex items-start gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 text-green-500">
                <i class='bx bx-user text-2xl'></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-primary mb-1">Meet the Creator</h3>
                <p class="text-gray-800">On the Resources Heaven site, you'll find an introduction to the creator/owner of the Prologware Solutions Community.</p>
            </div>
        </div>

        <!-- Step 3 -->
        <div class="flex items-start gap-4">
            <div class="w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-500">
                <i class='bx bx-category text-2xl'></i>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-primary mb-1">Explore Resources Heaven Tools</h3>
                <p class="text-gray-800">Check out the 'Resources Heaven Tools' section to access a wide range of categories in our extensive library.</p>
            </div>
        </div>
    </div>
    
    <div class="mt-6">
        <a href="https://resources-heaven.super.site/" class="inline-flex items-center px-4 py-2 text-white bg-blue-500 border border-transparent rounded-md text-sm font-medium hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class='bx bx-link-external'></i> Go to Resources Heaven
        </a>
    </div>
</section>

<!-- Requesting and Suggesting Resources Section -->
<section class="p-6 bg-white rounded-lg">
    <h2 class="text-2xl font-bold text-primary mb-4">Requesting and Suggesting Resources</h2>
    <p class="text-lg text-gray-700 mb-4">We value your input! Here's how you can request or suggest new resources:</p>
    
    <ul class="space-y-4">
        <!-- Item 1 -->
        <li class="flex items-start gap-4">
            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-500">
                <i class='bx bx-send text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Click on the "Request Resources" button.</span>
        </li>
        
        <!-- Item 2 -->
        <li class="flex items-start gap-4">
            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-500">
                <i class='bx bx-list-plus text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Fill out the form with a detailed description of the resource you need or suggest.</span>
        </li>
        
        <!-- Rules Text -->
        <li class="flex items-start gap-4 text-gray-600 text-lg font-medium">
            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 text-gray-500">
                <i class='bx bx-info-circle text-2xl'></i>
            </div>
            <span class="text-lg">Review the guidelines in the form to ensure your requests and suggestions meet our standards.</span>
        </li>
        
        <!-- Item 3 -->
        <li class="flex items-start gap-4">
            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-yellow-100 text-yellow-500">
                <i class='bx bxs-check-circle text-2xl'></i>
            </div>
            <span class="text-lg text-gray-800">Submit the form and await community feedback.</span>
        </li>
    </ul>
    
    <div class="mt-6">
        <a href="https://docs.google.com/forms/d/e/1FAIpQLSdKa4MB9goqYJYTbNhQCNE1GCjIBV43OazjQ2KU2CTXUHrNWQ/viewform?usp=sf_link">
            <button class="inline-flex items-center px-4 py-2 text-white bg-blue-500 border border-transparent rounded-md text-sm font-medium hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class='bx bx-link-external'></i> Request & Suggest Resources
            </button>
        </a>
    </div>
</section>

        <!-- Footer Section -->
        <footer class="mt-8">
            <p class="text-center text-gray-600">© 2024 Prologware Solutions. All rights reserved.</p>
            <div class="flex justify-center gap-4 mt-4">
                <!-- LinkedIn Link -->
                <a href="https://www.linkedin.com/company/prologware-solutions/" target="_blank" class="text-gray-600 hover:text-gray-900">
                    <i class='bx bxl-linkedin'></i>
                </a>
                <!-- GitHub Link -->
                <a href="https://github.com/abm1119" target="_blank" class="text-gray-600 hover:text-gray-900">
                    <i class='bx bxl-github'></i>
                </a>
                <!-- Facebook Link -->
                <a href="https://www.facebook.com/prologwaresolution/" target="_blank" class="text-gray-600 hover:text-gray-900">
                    <i class='bx bxl-facebook'></i>
                </a>
                <!-- Instagram Link -->
                <a href="https://www.instagram.com/prologware_solutions/?igsh=YzAwZjE1ZTI0Zg%3D%3D" target="_blank" class="text-gray-600 hover:text-gray-900">
                    <i class='bx bxl-instagram'></i>
                </a>
            </div>
        </footer>
        
    </div>

    <script src="https://unpkg.com/flowbite@1.6.5/dist/flowbite.min.js"></script>
    <!-- Material UI Icons -->
<script src="https://unpkg.com/material-icons/iconfont/material-icons.css"></script>
    <script>
    function toggleSection(sectionId) {
        const section = document.getElementById(sectionId);
        const icon = document.getElementById('icon' + sectionId.replace('section', ''));
        
        if (section.classList.contains('max-h-0')) {
            section.classList.remove('max-h-0');
            section.classList.add('max-h-screen'); // Adjusted to accommodate content height
            icon.textContent = 'expand_less';
        } else {
            section.classList.remove('max-h-screen');
            section.classList.add('max-h-0');
            icon.textContent = 'expand_more';
        }
    }
        const accordions = document.querySelectorAll('.accordion');

        accordions.forEach((accordion) => {
            accordion.addEventListener('click', function () {
                this.classList.toggle('active');
                const content = this.nextElementSibling;
                if (content.style.maxHeight) {
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = content.scrollHeight + 'px';
                }
            });
        });

        
    </script>
</body>
</html>
